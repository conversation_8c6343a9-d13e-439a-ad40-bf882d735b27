Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to motor_control.o(.text) for Motor_System_Init
    stm32f4xx_it.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_GetITStatus
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_can.o(.text) for CheckITStatus
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_IVInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Init
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_DataOut
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_GetStatus
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(.text) refers to stm32f4xx_flash.o(.text) for FLASH_WaitForLastOperation
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_Init
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DataIn
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_StartDigest
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetDigest
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_Init
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DataIn
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_StartDigest
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_Init
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DataIn
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_Init
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DataIn
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_GetDigest
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_GetClocksFreq
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_GetFlagStatus
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_EnterInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_EnterInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_ExitInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_EnterInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_ExitInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_EnterInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_ExitInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_EnterInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_ExitInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_EnterInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_ExitInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_EnterInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_ExitInitMode
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(.text) refers to stm32f4xx_rtc.o(.text) for RTC_WaitForSynchro
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TI1_Config
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TI1_Config
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TI2_Config
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_ETRConfig
    stm32f4xx_tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_ETRConfig
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_GetClocksFreq
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    atd5984.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    atd5984.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    atd5984.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    atd5984.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    atd5984.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    atd5984.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    atd5984.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    atd5984.o(.text) refers to noretval__2printf.o(.text) for __2printf
    atd5984.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    atd5984.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    atd5984.o(.text) refers to delay.o(.text) for delay_ms
    atd5984.o(.text) refers to atd5984.o(.data) for current_pwm_frequency
    atd5984.o(.text) refers to noretval__2printf.o(.text) for __2printf
    atd5984.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    atd5984.o(.text) refers to delay.o(.text) for delay_ms
    atd5984.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_CCxCmd
    atd5984.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    atd5984.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    atd5984.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    atd5984.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    atd5984.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    atd5984.o(.text) refers to noretval__2printf.o(.text) for __2printf
    atd5984.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    atd5984.o(.text) refers to delay.o(.text) for delay_ms
    atd5984.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_CCxCmd
    atd5984.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_ResetBits
    adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    adc.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_CommonInit
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_Init
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_Cmd
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_RegularChannelConfig
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_SoftwareStartConv
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_GetFlagStatus
    adc.o(.text) refers to stm32f4xx_adc.o(.text) for ADC_GetConversionValue
    adc.o(.text) refers to adc.o(.text) for Get_Adc1
    adc.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    tim.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_ITConfig
    tim.o(.text) refers to misc.o(.text) for NVIC_Init
    motor_control.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    motor_control.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    motor_control.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    motor_control.o(.text) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    motor_control.o(.text) refers to _printf_str.o(.text) for _printf_str
    motor_control.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    motor_control.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    motor_control.o(.text) refers to noretval__2printf.o(.text) for __2printf
    motor_control.o(.text) refers to atd5984.o(.text) for ATD5984_Init
    motor_control.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_CCxCmd
    motor_control.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    motor_control.o(.text) refers to motor_control.o(.data) for motor_state
    motor_control.o(.text) refers to motor_control.o(.text) for Motor_Stop
    motor_control.o(.text) refers to atd5984.o(.text) for Motor_A_Disable
    motor_control.o(.text) refers to noretval__2printf.o(.text) for __2printf
    motor_control.o(.text) refers to atd5984.o(.text) for Motor_B_Disable
    motor_control.o(.text) refers to motor_control.o(.data) for motor_state
    motor_control.o(.text) refers to noretval__2printf.o(.text) for __2printf
    motor_control.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_CCxCmd
    motor_control.o(.text) refers to atd5984.o(.text) for Motor_A_Disable
    motor_control.o(.text) refers to atd5984.o(.text) for Motor_B_Disable
    motor_control.o(.text) refers to motor_control.o(.data) for motor_state
    motor_control.o(.text) refers to motor_control.o(.data) for motor_state
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.text), (192 bytes).
    Removing system_stm32f4xx.o(.data), (20 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.text), (20 bytes).
    Removing misc.o(.text), (32 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (84 bytes).
    Removing stm32f4xx_adc.o(.text), (20 bytes).
    Removing stm32f4xx_adc.o(.text), (48 bytes).
    Removing stm32f4xx_adc.o(.text), (12 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (20 bytes).
    Removing stm32f4xx_adc.o(.text), (6 bytes).
    Removing stm32f4xx_adc.o(.text), (16 bytes).
    Removing stm32f4xx_adc.o(.text), (44 bytes).
    Removing stm32f4xx_adc.o(.text), (44 bytes).
    Removing stm32f4xx_adc.o(.text), (184 bytes).
    Removing stm32f4xx_adc.o(.text), (10 bytes).
    Removing stm32f4xx_adc.o(.text), (20 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (24 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (8 bytes).
    Removing stm32f4xx_adc.o(.text), (12 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (44 bytes).
    Removing stm32f4xx_adc.o(.text), (130 bytes).
    Removing stm32f4xx_adc.o(.text), (24 bytes).
    Removing stm32f4xx_adc.o(.text), (20 bytes).
    Removing stm32f4xx_adc.o(.text), (16 bytes).
    Removing stm32f4xx_adc.o(.text), (16 bytes).
    Removing stm32f4xx_adc.o(.text), (10 bytes).
    Removing stm32f4xx_adc.o(.text), (20 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (22 bytes).
    Removing stm32f4xx_adc.o(.text), (28 bytes).
    Removing stm32f4xx_adc.o(.text), (32 bytes).
    Removing stm32f4xx_adc.o(.text), (18 bytes).
    Removing stm32f4xx_adc.o(.text), (6 bytes).
    Removing stm32f4xx_adc.o(.text), (38 bytes).
    Removing stm32f4xx_adc.o(.text), (10 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (56 bytes).
    Removing stm32f4xx_can.o(.text), (276 bytes).
    Removing stm32f4xx_can.o(.text), (264 bytes).
    Removing stm32f4xx_can.o(.text), (32 bytes).
    Removing stm32f4xx_can.o(.text), (52 bytes).
    Removing stm32f4xx_can.o(.text), (22 bytes).
    Removing stm32f4xx_can.o(.text), (118 bytes).
    Removing stm32f4xx_can.o(.text), (294 bytes).
    Removing stm32f4xx_can.o(.text), (160 bytes).
    Removing stm32f4xx_can.o(.text), (48 bytes).
    Removing stm32f4xx_can.o(.text), (240 bytes).
    Removing stm32f4xx_can.o(.text), (22 bytes).
    Removing stm32f4xx_can.o(.text), (30 bytes).
    Removing stm32f4xx_can.o(.text), (162 bytes).
    Removing stm32f4xx_can.o(.text), (30 bytes).
    Removing stm32f4xx_can.o(.text), (48 bytes).
    Removing stm32f4xx_can.o(.text), (12 bytes).
    Removing stm32f4xx_can.o(.text), (10 bytes).
    Removing stm32f4xx_can.o(.text), (12 bytes).
    Removing stm32f4xx_can.o(.text), (18 bytes).
    Removing stm32f4xx_can.o(.text), (120 bytes).
    Removing stm32f4xx_can.o(.text), (56 bytes).
    Removing stm32f4xx_can.o(.text), (18 bytes).
    Removing stm32f4xx_can.o(.text), (288 bytes).
    Removing stm32f4xx_can.o(.text), (168 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (12 bytes).
    Removing stm32f4xx_crc.o(.text), (16 bytes).
    Removing stm32f4xx_crc.o(.text), (36 bytes).
    Removing stm32f4xx_crc.o(.text), (12 bytes).
    Removing stm32f4xx_crc.o(.text), (12 bytes).
    Removing stm32f4xx_crc.o(.text), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (20 bytes).
    Removing stm32f4xx_cryp.o(.text), (124 bytes).
    Removing stm32f4xx_cryp.o(.text), (12 bytes).
    Removing stm32f4xx_cryp.o(.text), (40 bytes).
    Removing stm32f4xx_cryp.o(.text), (20 bytes).
    Removing stm32f4xx_cryp.o(.text), (24 bytes).
    Removing stm32f4xx_cryp.o(.text), (12 bytes).
    Removing stm32f4xx_cryp.o(.text), (24 bytes).
    Removing stm32f4xx_cryp.o(.text), (20 bytes).
    Removing stm32f4xx_cryp.o(.text), (36 bytes).
    Removing stm32f4xx_cryp.o(.text), (12 bytes).
    Removing stm32f4xx_cryp.o(.text), (12 bytes).
    Removing stm32f4xx_cryp.o(.text), (264 bytes).
    Removing stm32f4xx_cryp.o(.text), (148 bytes).
    Removing stm32f4xx_cryp.o(.text), (36 bytes).
    Removing stm32f4xx_cryp.o(.text), (36 bytes).
    Removing stm32f4xx_cryp.o(.text), (24 bytes).
    Removing stm32f4xx_cryp.o(.text), (24 bytes).
    Removing stm32f4xx_cryp.o(.text), (40 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (494 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (540 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (466 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (1308 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (1778 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (224 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (248 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (256 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (280 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (22 bytes).
    Removing stm32f4xx_dac.o(.text), (52 bytes).
    Removing stm32f4xx_dac.o(.text), (12 bytes).
    Removing stm32f4xx_dac.o(.text), (40 bytes).
    Removing stm32f4xx_dac.o(.text), (44 bytes).
    Removing stm32f4xx_dac.o(.text), (36 bytes).
    Removing stm32f4xx_dac.o(.text), (40 bytes).
    Removing stm32f4xx_dac.o(.text), (32 bytes).
    Removing stm32f4xx_dac.o(.text), (32 bytes).
    Removing stm32f4xx_dac.o(.text), (36 bytes).
    Removing stm32f4xx_dac.o(.text), (36 bytes).
    Removing stm32f4xx_dac.o(.text), (44 bytes).
    Removing stm32f4xx_dac.o(.text), (40 bytes).
    Removing stm32f4xx_dac.o(.text), (32 bytes).
    Removing stm32f4xx_dac.o(.text), (16 bytes).
    Removing stm32f4xx_dac.o(.text), (48 bytes).
    Removing stm32f4xx_dac.o(.text), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (12 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (32 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (32 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (32 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (28 bytes).
    Removing stm32f4xx_dcmi.o(.text), (64 bytes).
    Removing stm32f4xx_dcmi.o(.text), (18 bytes).
    Removing stm32f4xx_dcmi.o(.text), (32 bytes).
    Removing stm32f4xx_dcmi.o(.text), (36 bytes).
    Removing stm32f4xx_dcmi.o(.text), (32 bytes).
    Removing stm32f4xx_dcmi.o(.text), (36 bytes).
    Removing stm32f4xx_dcmi.o(.text), (36 bytes).
    Removing stm32f4xx_dcmi.o(.text), (36 bytes).
    Removing stm32f4xx_dcmi.o(.text), (12 bytes).
    Removing stm32f4xx_dcmi.o(.text), (36 bytes).
    Removing stm32f4xx_dcmi.o(.text), (52 bytes).
    Removing stm32f4xx_dcmi.o(.text), (12 bytes).
    Removing stm32f4xx_dcmi.o(.text), (28 bytes).
    Removing stm32f4xx_dcmi.o(.text), (12 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (22 bytes).
    Removing stm32f4xx_dma2d.o(.text), (228 bytes).
    Removing stm32f4xx_dma2d.o(.text), (24 bytes).
    Removing stm32f4xx_dma2d.o(.text), (20 bytes).
    Removing stm32f4xx_dma2d.o(.text), (20 bytes).
    Removing stm32f4xx_dma2d.o(.text), (36 bytes).
    Removing stm32f4xx_dma2d.o(.text), (184 bytes).
    Removing stm32f4xx_dma2d.o(.text), (26 bytes).
    Removing stm32f4xx_dma2d.o(.text), (184 bytes).
    Removing stm32f4xx_dma2d.o(.text), (26 bytes).
    Removing stm32f4xx_dma2d.o(.text), (36 bytes).
    Removing stm32f4xx_dma2d.o(.text), (36 bytes).
    Removing stm32f4xx_dma2d.o(.text), (56 bytes).
    Removing stm32f4xx_dma2d.o(.text), (12 bytes).
    Removing stm32f4xx_dma2d.o(.text), (32 bytes).
    Removing stm32f4xx_dma2d.o(.text), (24 bytes).
    Removing stm32f4xx_dma2d.o(.text), (12 bytes).
    Removing stm32f4xx_dma2d.o(.text), (44 bytes).
    Removing stm32f4xx_dma2d.o(.text), (12 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (344 bytes).
    Removing stm32f4xx_dma.o(.text), (88 bytes).
    Removing stm32f4xx_dma.o(.text), (34 bytes).
    Removing stm32f4xx_dma.o(.text), (22 bytes).
    Removing stm32f4xx_dma.o(.text), (22 bytes).
    Removing stm32f4xx_dma.o(.text), (22 bytes).
    Removing stm32f4xx_dma.o(.text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (8 bytes).
    Removing stm32f4xx_dma.o(.text), (24 bytes).
    Removing stm32f4xx_dma.o(.text), (22 bytes).
    Removing stm32f4xx_dma.o(.text), (10 bytes).
    Removing stm32f4xx_dma.o(.text), (20 bytes).
    Removing stm32f4xx_dma.o(.text), (20 bytes).
    Removing stm32f4xx_dma.o(.text), (12 bytes).
    Removing stm32f4xx_dma.o(.text), (68 bytes).
    Removing stm32f4xx_dma.o(.text), (52 bytes).
    Removing stm32f4xx_dma.o(.text), (58 bytes).
    Removing stm32f4xx_dma.o(.text), (100 bytes).
    Removing stm32f4xx_dma.o(.text), (52 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (36 bytes).
    Removing stm32f4xx_exti.o(.text), (148 bytes).
    Removing stm32f4xx_exti.o(.text), (16 bytes).
    Removing stm32f4xx_exti.o(.text), (16 bytes).
    Removing stm32f4xx_exti.o(.text), (24 bytes).
    Removing stm32f4xx_exti.o(.text), (12 bytes).
    Removing stm32f4xx_exti.o(.text), (24 bytes).
    Removing stm32f4xx_exti.o(.text), (12 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (12 bytes).
    Removing stm32f4xx_flash.o(.text), (36 bytes).
    Removing stm32f4xx_flash.o(.text), (36 bytes).
    Removing stm32f4xx_flash.o(.text), (36 bytes).
    Removing stm32f4xx_flash.o(.text), (20 bytes).
    Removing stm32f4xx_flash.o(.text), (20 bytes).
    Removing stm32f4xx_flash.o(.text), (36 bytes).
    Removing stm32f4xx_flash.o(.text), (20 bytes).
    Removing stm32f4xx_flash.o(.text), (84 bytes).
    Removing stm32f4xx_flash.o(.text), (34 bytes).
    Removing stm32f4xx_flash.o(.text), (136 bytes).
    Removing stm32f4xx_flash.o(.text), (108 bytes).
    Removing stm32f4xx_flash.o(.text), (108 bytes).
    Removing stm32f4xx_flash.o(.text), (108 bytes).
    Removing stm32f4xx_flash.o(.text), (84 bytes).
    Removing stm32f4xx_flash.o(.text), (80 bytes).
    Removing stm32f4xx_flash.o(.text), (80 bytes).
    Removing stm32f4xx_flash.o(.text), (76 bytes).
    Removing stm32f4xx_flash.o(.text), (36 bytes).
    Removing stm32f4xx_flash.o(.text), (20 bytes).
    Removing stm32f4xx_flash.o(.text), (52 bytes).
    Removing stm32f4xx_flash.o(.text), (52 bytes).
    Removing stm32f4xx_flash.o(.text), (24 bytes).
    Removing stm32f4xx_flash.o(.text), (52 bytes).
    Removing stm32f4xx_flash.o(.text), (52 bytes).
    Removing stm32f4xx_flash.o(.text), (28 bytes).
    Removing stm32f4xx_flash.o(.text), (48 bytes).
    Removing stm32f4xx_flash.o(.text), (28 bytes).
    Removing stm32f4xx_flash.o(.text), (28 bytes).
    Removing stm32f4xx_flash.o(.text), (32 bytes).
    Removing stm32f4xx_flash.o(.text), (16 bytes).
    Removing stm32f4xx_flash.o(.text), (12 bytes).
    Removing stm32f4xx_flash.o(.text), (12 bytes).
    Removing stm32f4xx_flash.o(.text), (12 bytes).
    Removing stm32f4xx_flash.o(.text), (12 bytes).
    Removing stm32f4xx_flash.o(.text), (24 bytes).
    Removing stm32f4xx_flash.o(.text), (16 bytes).
    Removing stm32f4xx_flash.o(.text), (32 bytes).
    Removing stm32f4xx_flash.o(.text), (24 bytes).
    Removing stm32f4xx_flash.o(.text), (12 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (36 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (36 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (54 bytes).
    Removing stm32f4xx_fsmc.o(.text), (230 bytes).
    Removing stm32f4xx_fsmc.o(.text), (52 bytes).
    Removing stm32f4xx_fsmc.o(.text), (52 bytes).
    Removing stm32f4xx_fsmc.o(.text), (68 bytes).
    Removing stm32f4xx_fsmc.o(.text), (136 bytes).
    Removing stm32f4xx_fsmc.o(.text), (54 bytes).
    Removing stm32f4xx_fsmc.o(.text), (92 bytes).
    Removing stm32f4xx_fsmc.o(.text), (92 bytes).
    Removing stm32f4xx_fsmc.o(.text), (28 bytes).
    Removing stm32f4xx_fsmc.o(.text), (40 bytes).
    Removing stm32f4xx_fsmc.o(.text), (132 bytes).
    Removing stm32f4xx_fsmc.o(.text), (60 bytes).
    Removing stm32f4xx_fsmc.o(.text), (48 bytes).
    Removing stm32f4xx_fsmc.o(.text), (128 bytes).
    Removing stm32f4xx_fsmc.o(.text), (56 bytes).
    Removing stm32f4xx_fsmc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.text), (68 bytes).
    Removing stm32f4xx_fsmc.o(.text), (72 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.text), (312 bytes).
    Removing stm32f4xx_gpio.o(.text), (18 bytes).
    Removing stm32f4xx_gpio.o(.text), (34 bytes).
    Removing stm32f4xx_gpio.o(.text), (18 bytes).
    Removing stm32f4xx_gpio.o(.text), (8 bytes).
    Removing stm32f4xx_gpio.o(.text), (18 bytes).
    Removing stm32f4xx_gpio.o(.text), (8 bytes).
    Removing stm32f4xx_gpio.o(.text), (10 bytes).
    Removing stm32f4xx_gpio.o(.text), (4 bytes).
    Removing stm32f4xx_gpio.o(.text), (8 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (20 bytes).
    Removing stm32f4xx_hash.o(.text), (84 bytes).
    Removing stm32f4xx_hash.o(.text), (12 bytes).
    Removing stm32f4xx_hash.o(.text), (20 bytes).
    Removing stm32f4xx_hash.o(.text), (28 bytes).
    Removing stm32f4xx_hash.o(.text), (12 bytes).
    Removing stm32f4xx_hash.o(.text), (16 bytes).
    Removing stm32f4xx_hash.o(.text), (72 bytes).
    Removing stm32f4xx_hash.o(.text), (20 bytes).
    Removing stm32f4xx_hash.o(.text), (60 bytes).
    Removing stm32f4xx_hash.o(.text), (68 bytes).
    Removing stm32f4xx_hash.o(.text), (36 bytes).
    Removing stm32f4xx_hash.o(.text), (36 bytes).
    Removing stm32f4xx_hash.o(.text), (32 bytes).
    Removing stm32f4xx_hash.o(.text), (44 bytes).
    Removing stm32f4xx_hash.o(.text), (12 bytes).
    Removing stm32f4xx_hash.o(.text), (32 bytes).
    Removing stm32f4xx_hash.o(.text), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (180 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (354 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (186 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (362 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (96 bytes).
    Removing stm32f4xx_i2c.o(.text), (232 bytes).
    Removing stm32f4xx_i2c.o(.text), (30 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (22 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (16 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (22 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (22 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (28 bytes).
    Removing stm32f4xx_i2c.o(.text), (28 bytes).
    Removing stm32f4xx_i2c.o(.text), (28 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (8 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (28 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (8 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (24 bytes).
    Removing stm32f4xx_i2c.o(.text), (22 bytes).
    Removing stm32f4xx_i2c.o(.text), (18 bytes).
    Removing stm32f4xx_i2c.o(.text), (42 bytes).
    Removing stm32f4xx_i2c.o(.text), (26 bytes).
    Removing stm32f4xx_i2c.o(.text), (58 bytes).
    Removing stm32f4xx_i2c.o(.text), (12 bytes).
    Removing stm32f4xx_i2c.o(.text), (38 bytes).
    Removing stm32f4xx_i2c.o(.text), (12 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (12 bytes).
    Removing stm32f4xx_iwdg.o(.text), (12 bytes).
    Removing stm32f4xx_iwdg.o(.text), (12 bytes).
    Removing stm32f4xx_iwdg.o(.text), (16 bytes).
    Removing stm32f4xx_iwdg.o(.text), (16 bytes).
    Removing stm32f4xx_iwdg.o(.text), (24 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (22 bytes).
    Removing stm32f4xx_ltdc.o(.text), (392 bytes).
    Removing stm32f4xx_ltdc.o(.text), (34 bytes).
    Removing stm32f4xx_ltdc.o(.text), (36 bytes).
    Removing stm32f4xx_ltdc.o(.text), (36 bytes).
    Removing stm32f4xx_ltdc.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.text), (10 bytes).
    Removing stm32f4xx_ltdc.o(.text), (12 bytes).
    Removing stm32f4xx_ltdc.o(.text), (12 bytes).
    Removing stm32f4xx_ltdc.o(.text), (276 bytes).
    Removing stm32f4xx_ltdc.o(.text), (48 bytes).
    Removing stm32f4xx_ltdc.o(.text), (22 bytes).
    Removing stm32f4xx_ltdc.o(.text), (44 bytes).
    Removing stm32f4xx_ltdc.o(.text), (8 bytes).
    Removing stm32f4xx_ltdc.o(.text), (24 bytes).
    Removing stm32f4xx_ltdc.o(.text), (68 bytes).
    Removing stm32f4xx_ltdc.o(.text), (10 bytes).
    Removing stm32f4xx_ltdc.o(.text), (32 bytes).
    Removing stm32f4xx_ltdc.o(.text), (32 bytes).
    Removing stm32f4xx_ltdc.o(.text), (12 bytes).
    Removing stm32f4xx_ltdc.o(.text), (160 bytes).
    Removing stm32f4xx_ltdc.o(.text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (116 bytes).
    Removing stm32f4xx_ltdc.o(.text), (106 bytes).
    Removing stm32f4xx_ltdc.o(.text), (32 bytes).
    Removing stm32f4xx_ltdc.o(.text), (24 bytes).
    Removing stm32f4xx_ltdc.o(.text), (12 bytes).
    Removing stm32f4xx_ltdc.o(.text), (44 bytes).
    Removing stm32f4xx_ltdc.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (22 bytes).
    Removing stm32f4xx_pwr.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.text), (24 bytes).
    Removing stm32f4xx_pwr.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.text), (24 bytes).
    Removing stm32f4xx_pwr.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.text), (36 bytes).
    Removing stm32f4xx_pwr.o(.text), (24 bytes).
    Removing stm32f4xx_pwr.o(.text), (24 bytes).
    Removing stm32f4xx_pwr.o(.text), (12 bytes).
    Removing stm32f4xx_pwr.o(.text), (68 bytes).
    Removing stm32f4xx_pwr.o(.text), (68 bytes).
    Removing stm32f4xx_pwr.o(.text), (40 bytes).
    Removing stm32f4xx_pwr.o(.text), (24 bytes).
    Removing stm32f4xx_pwr.o(.text), (20 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.text), (100 bytes).
    Removing stm32f4xx_rcc.o(.text), (16 bytes).
    Removing stm32f4xx_rcc.o(.text), (64 bytes).
    Removing stm32f4xx_rcc.o(.text), (56 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (44 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (36 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (16 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (28 bytes).
    Removing stm32f4xx_rcc.o(.text), (28 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (16 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (60 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (28 bytes).
    Removing stm32f4xx_rcc.o(.text), (28 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (36 bytes).
    Removing stm32f4xx_rcc.o(.text), (32 bytes).
    Removing stm32f4xx_rcc.o(.text), (20 bytes).
    Removing stm32f4xx_rcc.o(.text), (24 bytes).
    Removing stm32f4xx_rcc.o(.text), (12 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (20 bytes).
    Removing stm32f4xx_rng.o(.text), (36 bytes).
    Removing stm32f4xx_rng.o(.text), (12 bytes).
    Removing stm32f4xx_rng.o(.text), (36 bytes).
    Removing stm32f4xx_rng.o(.text), (24 bytes).
    Removing stm32f4xx_rng.o(.text), (16 bytes).
    Removing stm32f4xx_rng.o(.text), (24 bytes).
    Removing stm32f4xx_rng.o(.text), (16 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (96 bytes).
    Removing stm32f4xx_rtc.o(.text), (80 bytes).
    Removing stm32f4xx_rtc.o(.text), (212 bytes).
    Removing stm32f4xx_rtc.o(.text), (20 bytes).
    Removing stm32f4xx_rtc.o(.text), (100 bytes).
    Removing stm32f4xx_rtc.o(.text), (14 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (80 bytes).
    Removing stm32f4xx_rtc.o(.text), (60 bytes).
    Removing stm32f4xx_rtc.o(.text), (50 bytes).
    Removing stm32f4xx_rtc.o(.text), (208 bytes).
    Removing stm32f4xx_rtc.o(.text), (12 bytes).
    Removing stm32f4xx_rtc.o(.text), (80 bytes).
    Removing stm32f4xx_rtc.o(.text), (20 bytes).
    Removing stm32f4xx_rtc.o(.text), (200 bytes).
    Removing stm32f4xx_rtc.o(.text), (14 bytes).
    Removing stm32f4xx_rtc.o(.text), (76 bytes).
    Removing stm32f4xx_rtc.o(.text), (236 bytes).
    Removing stm32f4xx_rtc.o(.text), (22 bytes).
    Removing stm32f4xx_rtc.o(.text), (116 bytes).
    Removing stm32f4xx_rtc.o(.text), (116 bytes).
    Removing stm32f4xx_rtc.o(.text), (52 bytes).
    Removing stm32f4xx_rtc.o(.text), (36 bytes).
    Removing stm32f4xx_rtc.o(.text), (48 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (12 bytes).
    Removing stm32f4xx_rtc.o(.text), (120 bytes).
    Removing stm32f4xx_rtc.o(.text), (56 bytes).
    Removing stm32f4xx_rtc.o(.text), (16 bytes).
    Removing stm32f4xx_rtc.o(.text), (56 bytes).
    Removing stm32f4xx_rtc.o(.text), (56 bytes).
    Removing stm32f4xx_rtc.o(.text), (80 bytes).
    Removing stm32f4xx_rtc.o(.text), (60 bytes).
    Removing stm32f4xx_rtc.o(.text), (48 bytes).
    Removing stm32f4xx_rtc.o(.text), (96 bytes).
    Removing stm32f4xx_rtc.o(.text), (56 bytes).
    Removing stm32f4xx_rtc.o(.text), (156 bytes).
    Removing stm32f4xx_rtc.o(.text), (12 bytes).
    Removing stm32f4xx_rtc.o(.text), (36 bytes).
    Removing stm32f4xx_rtc.o(.text), (32 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (36 bytes).
    Removing stm32f4xx_rtc.o(.text), (36 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (32 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (124 bytes).
    Removing stm32f4xx_rtc.o(.text), (100 bytes).
    Removing stm32f4xx_rtc.o(.text), (40 bytes).
    Removing stm32f4xx_rtc.o(.text), (28 bytes).
    Removing stm32f4xx_rtc.o(.text), (64 bytes).
    Removing stm32f4xx_rtc.o(.text), (32 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (24 bytes).
    Removing stm32f4xx_sai.o(.text), (72 bytes).
    Removing stm32f4xx_sai.o(.text), (44 bytes).
    Removing stm32f4xx_sai.o(.text), (36 bytes).
    Removing stm32f4xx_sai.o(.text), (28 bytes).
    Removing stm32f4xx_sai.o(.text), (18 bytes).
    Removing stm32f4xx_sai.o(.text), (16 bytes).
    Removing stm32f4xx_sai.o(.text), (22 bytes).
    Removing stm32f4xx_sai.o(.text), (18 bytes).
    Removing stm32f4xx_sai.o(.text), (18 bytes).
    Removing stm32f4xx_sai.o(.text), (16 bytes).
    Removing stm32f4xx_sai.o(.text), (22 bytes).
    Removing stm32f4xx_sai.o(.text), (16 bytes).
    Removing stm32f4xx_sai.o(.text), (18 bytes).
    Removing stm32f4xx_sai.o(.text), (10 bytes).
    Removing stm32f4xx_sai.o(.text), (6 bytes).
    Removing stm32f4xx_sai.o(.text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (22 bytes).
    Removing stm32f4xx_sai.o(.text), (18 bytes).
    Removing stm32f4xx_sai.o(.text), (18 bytes).
    Removing stm32f4xx_sai.o(.text), (8 bytes).
    Removing stm32f4xx_sai.o(.text), (30 bytes).
    Removing stm32f4xx_sai.o(.text), (8 bytes).
    Removing stm32f4xx_sai.o(.text), (20 bytes).
    Removing stm32f4xx_sai.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (22 bytes).
    Removing stm32f4xx_sdio.o(.text), (48 bytes).
    Removing stm32f4xx_sdio.o(.text), (16 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (16 bytes).
    Removing stm32f4xx_sdio.o(.text), (44 bytes).
    Removing stm32f4xx_sdio.o(.text), (14 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (24 bytes).
    Removing stm32f4xx_sdio.o(.text), (52 bytes).
    Removing stm32f4xx_sdio.o(.text), (20 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (16 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (32 bytes).
    Removing stm32f4xx_sdio.o(.text), (24 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_sdio.o(.text), (24 bytes).
    Removing stm32f4xx_sdio.o(.text), (12 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (176 bytes).
    Removing stm32f4xx_spi.o(.text), (60 bytes).
    Removing stm32f4xx_spi.o(.text), (408 bytes).
    Removing stm32f4xx_spi.o(.text), (24 bytes).
    Removing stm32f4xx_spi.o(.text), (20 bytes).
    Removing stm32f4xx_spi.o(.text), (24 bytes).
    Removing stm32f4xx_spi.o(.text), (24 bytes).
    Removing stm32f4xx_spi.o(.text), (18 bytes).
    Removing stm32f4xx_spi.o(.text), (28 bytes).
    Removing stm32f4xx_spi.o(.text), (30 bytes).
    Removing stm32f4xx_spi.o(.text), (24 bytes).
    Removing stm32f4xx_spi.o(.text), (24 bytes).
    Removing stm32f4xx_spi.o(.text), (80 bytes).
    Removing stm32f4xx_spi.o(.text), (6 bytes).
    Removing stm32f4xx_spi.o(.text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (24 bytes).
    Removing stm32f4xx_spi.o(.text), (10 bytes).
    Removing stm32f4xx_spi.o(.text), (16 bytes).
    Removing stm32f4xx_spi.o(.text), (6 bytes).
    Removing stm32f4xx_spi.o(.text), (18 bytes).
    Removing stm32f4xx_spi.o(.text), (32 bytes).
    Removing stm32f4xx_spi.o(.text), (18 bytes).
    Removing stm32f4xx_spi.o(.text), (6 bytes).
    Removing stm32f4xx_spi.o(.text), (52 bytes).
    Removing stm32f4xx_spi.o(.text), (20 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (22 bytes).
    Removing stm32f4xx_syscfg.o(.text), (12 bytes).
    Removing stm32f4xx_syscfg.o(.text), (12 bytes).
    Removing stm32f4xx_syscfg.o(.text), (64 bytes).
    Removing stm32f4xx_syscfg.o(.text), (12 bytes).
    Removing stm32f4xx_syscfg.o(.text), (12 bytes).
    Removing stm32f4xx_syscfg.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (400 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (164 bytes).
    Removing stm32f4xx_tim.o(.text), (120 bytes).
    Removing stm32f4xx_tim.o(.text), (20 bytes).
    Removing stm32f4xx_tim.o(.text), (86 bytes).
    Removing stm32f4xx_tim.o(.text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (4 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (30 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (26 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (110 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (124 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (32 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_tim.o(.text), (10 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (12 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (62 bytes).
    Removing stm32f4xx_tim.o(.text), (28 bytes).
    Removing stm32f4xx_tim.o(.text), (54 bytes).
    Removing stm32f4xx_tim.o(.text), (32 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (18 bytes).
    Removing stm32f4xx_tim.o(.text), (66 bytes).
    Removing stm32f4xx_tim.o(.text), (24 bytes).
    Removing stm32f4xx_tim.o(.text), (6 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.text), (240 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (32 bytes).
    Removing stm32f4xx_usart.o(.text), (12 bytes).
    Removing stm32f4xx_usart.o(.text), (16 bytes).
    Removing stm32f4xx_usart.o(.text), (22 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (8 bytes).
    Removing stm32f4xx_usart.o(.text), (18 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (18 bytes).
    Removing stm32f4xx_usart.o(.text), (18 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (10 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (16 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (18 bytes).
    Removing stm32f4xx_usart.o(.text), (24 bytes).
    Removing stm32f4xx_usart.o(.text), (18 bytes).
    Removing stm32f4xx_usart.o(.text), (26 bytes).
    Removing stm32f4xx_usart.o(.text), (18 bytes).
    Removing stm32f4xx_usart.o(.text), (30 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (22 bytes).
    Removing stm32f4xx_wwdg.o(.text), (24 bytes).
    Removing stm32f4xx_wwdg.o(.text), (40 bytes).
    Removing stm32f4xx_wwdg.o(.text), (12 bytes).
    Removing stm32f4xx_wwdg.o(.text), (16 bytes).
    Removing stm32f4xx_wwdg.o(.text), (16 bytes).
    Removing stm32f4xx_wwdg.o(.text), (20 bytes).
    Removing stm32f4xx_wwdg.o(.text), (12 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.text), (76 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing atd5984.o(.rev16_text), (4 bytes).
    Removing atd5984.o(.revsh_text), (4 bytes).
    Removing atd5984.o(.text), (576 bytes).
    Removing atd5984.o(.text), (700 bytes).
    Removing atd5984.o(.text), (16 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.text), (120 bytes).
    Removing adc.o(.text), (48 bytes).
    Removing adc.o(.text), (46 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.text), (48 bytes).
    Removing key.o(.text), (20 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.text), (86 bytes).
    Removing motor_control.o(.rev16_text), (4 bytes).
    Removing motor_control.o(.revsh_text), (4 bytes).
    Removing motor_control.o(.text), (128 bytes).
    Removing motor_control.o(.text), (132 bytes).
    Removing motor_control.o(.text), (32 bytes).

782 unused section(s) (total 36186 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\HAREWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HAREWARE\ATD5984\ATD5984.c            0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\HAREWARE\KEY\KEY.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HAREWARE\MOTOR_CONTROL\motor_control.c 0x00000000   Number         0  motor_control.o ABSOLUTE
    ..\HAREWARE\TIM\TIM.c                    0x00000000   Number         0  tim.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\HAREWARE\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HAREWARE\\ATD5984\\ATD5984.c         0x00000000   Number         0  atd5984.o ABSOLUTE
    ..\\HAREWARE\\KEY\\KEY.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HAREWARE\\MOTOR_CONTROL\\motor_control.c 0x00000000   Number         0  motor_control.o ABSOLUTE
    ..\\HAREWARE\\TIM\\TIM.c                 0x00000000   Number         0  tim.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000014  0x08000208   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x0800020e   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000212   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000214   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000218   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x0800021e   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000228   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000228   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800022a   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x0800022c   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x0800022c   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x0800022e   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800022e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800022e   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000234   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000234   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000238   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000238   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000240   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000242   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000242   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000246   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0800024c   Section        0  main.o(.text)
    .text                                    0x08000578   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x080005a8   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x080005a9   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x080006f8   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x080006f8   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000738   Section        0  misc.o(.text)
    .text                                    0x080007e4   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x080008c4   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x080009f0   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x08000bd3   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08000c23   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08000c6b   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI1_Config                               0x08000cbb   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08000d5c   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x08000ef0   Section        0  delay.o(.text)
    .text                                    0x08000fac   Section        0  usart.o(.text)
    .text                                    0x080010f4   Section        0  atd5984.o(.text)
    .text                                    0x08001ad8   Section        0  motor_control.o(.text)
    .text                                    0x08001e78   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08001e7c   Section        0  noretval__2printf.o(.text)
    .text                                    0x08001e94   Section        0  _printf_str.o(.text)
    .text                                    0x08001ee8   Section        0  _printf_dec.o(.text)
    .text                                    0x08001f60   Section        0  __printf_wp.o(.text)
    .text                                    0x0800206e   Section        0  heapauxi.o(.text)
    .text                                    0x08002074   Section        2  use_no_semi.o(.text)
    .text                                    0x08002076   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08002128   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800212b   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08002546   Section        0  _printf_char.o(.text)
    .text                                    0x08002574   Section        0  _printf_char_file.o(.text)
    .text                                    0x08002598   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080025a0   Section      138  lludiv10.o(.text)
    .text                                    0x0800262c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800262d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800265c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080026dc   Section        0  bigflt0.o(.text)
    .text                                    0x080027c0   Section        0  ferror.o(.text)
    .text                                    0x080027c8   Section        8  libspace.o(.text)
    .text                                    0x080027d0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800281a   Section        0  exit.o(.text)
    .text                                    0x0800282c   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x080028ac   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080028ea   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08002930   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08002990   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08002cc8   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002da4   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08002dce   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08002df8   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x0800303c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x0800306c   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x0800307c   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dretinf                            0x080030a8   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x080030a8   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x080030b4   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x080030b4   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800310a   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800310a   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08003196   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08003196   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080031a0   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080031a0   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x080031a4   Section      148  bigflt0.o(.constdata)
    x$fpl$usenofp                            0x080031a4   Section        0  usenofp.o(x$fpl$usenofp)
    tenpwrs_x                                0x080031a4   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080031e0   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08003258   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800325c   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08003264   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08003270   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08003272   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08003273   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08003274   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000000   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000010   Section        4  delay.o(.data)
    fac_us                                   0x20000010   Data           1  delay.o(.data)
    fac_ms                                   0x20000012   Data           2  delay.o(.data)
    .data                                    0x20000014   Section        6  usart.o(.data)
    .data                                    0x2000001a   Section        4  atd5984.o(.data)
    current_pwm_frequency                    0x2000001a   Data           2  atd5984.o(.data)
    current_pwm_frequency_motorB             0x2000001c   Data           2  atd5984.o(.data)
    .data                                    0x2000001e   Section        2  motor_control.o(.data)
    motor_state                              0x2000001e   Data           2  motor_control.o(.data)
    .bss                                     0x20000020   Section      200  usart.o(.bss)
    .bss                                     0x200000e8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000148   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x20000148   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20000348   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20000348   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000748   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_s                                0x08000209   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x0800020f   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000213   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000215   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000229   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x0800022b   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x0800022d   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x0800022f   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800022f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800022f   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000235   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000239   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000239   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000241   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000243   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000243   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000247   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x0800024d   Thumb Code   262  main.o(.text)
    NMI_Handler                              0x08000579   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800057b   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x0800057f   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x08000583   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x08000587   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800058b   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x0800058d   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x0800058f   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000591   Thumb Code     2  stm32f4xx_it.o(.text)
    TIM2_IRQHandler                          0x08000593   Thumb Code    22  stm32f4xx_it.o(.text)
    SystemInit                               0x08000685   Thumb Code    88  system_stm32f4xx.o(.text)
    Reset_Handler                            0x080006f9   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000713   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000715   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08000739   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08000743   Thumb Code   106  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080007ad   Thumb Code    40  misc.o(.text)
    GPIO_Init                                0x080007e5   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08000875   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08000879   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x0800087d   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_GetClocksFreq                        0x080008c5   Thumb Code   214  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x0800099b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x080009bd   Thumb Code    34  stm32f4xx_rcc.o(.text)
    TIM_TimeBaseInit                         0x080009f1   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08000a59   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x08000a71   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08000a89   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08000afb   Thumb Code   150  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x08000b91   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08000ba3   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08000bb5   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08000cf5   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08000d13   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x08000d17   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08000d39   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_Init                               0x08000d5d   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08000e29   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08000e41   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08000e4b   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08000e95   Thumb Code    84  stm32f4xx_usart.o(.text)
    delay_init                               0x08000ef1   Thumb Code    52  delay.o(.text)
    delay_xms                                0x08000f25   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08000f6d   Thumb Code    56  delay.o(.text)
    _sys_exit                                0x08000fad   Thumb Code     4  usart.o(.text)
    fputc                                    0x08000fb1   Thumb Code    22  usart.o(.text)
    uart_init                                0x08000fc7   Thumb Code   164  usart.o(.text)
    USART1_IRQHandler                        0x0800106b   Thumb Code   122  usart.o(.text)
    Motor_B_Disable                          0x080010f5   Thumb Code    14  atd5984.o(.text)
    ATD5984_Init                             0x08001103   Thumb Code   180  atd5984.o(.text)
    STEP12_PWM_Init                          0x080011b7   Thumb Code   172  atd5984.o(.text)
    Motor_A_Rotate                           0x08001263   Thumb Code   264  atd5984.o(.text)
    STEP_B_PWM_Init                          0x0800136b   Thumb Code   192  atd5984.o(.text)
    TIM8_SetFrequency                        0x0800142b   Thumb Code   158  atd5984.o(.text)
    TIM8_GetCurrentFrequency                 0x080014c9   Thumb Code     6  atd5984.o(.text)
    TIM1_SetFrequency                        0x080014cf   Thumb Code   754  atd5984.o(.text)
    TIM1_GetCurrentFrequency                 0x080017c1   Thumb Code     6  atd5984.o(.text)
    Motor_B_Rotate                           0x080017c7   Thumb Code   276  atd5984.o(.text)
    Motor_B_Enable                           0x080018db   Thumb Code    14  atd5984.o(.text)
    Motor_A_Enable                           0x080018e9   Thumb Code    12  atd5984.o(.text)
    Motor_System_Init                        0x08001ad9   Thumb Code    50  motor_control.o(.text)
    Motor_Enable                             0x08001b0b   Thumb Code    52  motor_control.o(.text)
    Motor_Stop                               0x08001b3f   Thumb Code    64  motor_control.o(.text)
    Motor_SetSpeed                           0x08001b7f   Thumb Code    98  motor_control.o(.text)
    Motor_Rotate                             0x08001be1   Thumb Code   130  motor_control.o(.text)
    Motor_GetState                           0x08001c63   Thumb Code    16  motor_control.o(.text)
    Motor_GetSpeed                           0x08001c73   Thumb Code    26  motor_control.o(.text)
    __use_no_semihosting                     0x08001e79   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08001e7d   Thumb Code    20  noretval__2printf.o(.text)
    _printf_str                              0x08001e95   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08001ee9   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x08001f61   Thumb Code   270  __printf_wp.o(.text)
    __use_two_region_memory                  0x0800206f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08002071   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08002073   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08002075   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08002075   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08002077   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08002129   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080022db   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_cs_common                        0x08002547   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800255b   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800256b   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08002575   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x08002599   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x080025a1   Thumb Code   138  lludiv10.o(.text)
    _printf_char_common                      0x08002637   Thumb Code    32  _printf_char_common.o(.text)
    _printf_fp_infnan                        0x0800265d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080026dd   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x080027c1   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x080027c9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080027c9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080027c9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080027d1   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800281b   Thumb Code    18  exit.o(.text)
    strcmp                                   0x0800282d   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x080028ad   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080028eb   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08002931   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08002991   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08002cc9   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002da5   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08002dcf   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08002df9   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x0800303d   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x0800306d   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x0800307d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __fpl_dretinf                            0x080030a9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x080030b5   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080030b5   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800310b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08003197   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800319f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800319f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080031a1   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x080031a4   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08003238   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003258   Number         0  anon$$obj.o(Region$$Table)
    __stdout                                 0x20000014   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000018   Data           2  usart.o(.data)
    USART_RX_BUF                             0x20000020   Data         200  usart.o(.bss)
    __libspace_start                         0x200000e8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000148   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003294, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003274, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          251    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5119  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         5345    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         5347    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         5349    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         5116    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         5115    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x08000202   0x00000006   Code   RO         5114    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x08000208   0x00000006   Code   RO         5113    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         5136    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000212   0x08000212   0x00000002   Code   RO         5220    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000214   0x08000214   0x00000004   Code   RO         5221    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         5224    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         5227    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         5229    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000000   Code   RO         5231    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000218   0x08000218   0x00000006   Code   RO         5232    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         5234    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         5236    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         5238    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x0000000a   Code   RO         5239    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5240    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5242    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5244    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5246    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5248    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5250    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5252    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5254    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5258    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5260    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5262    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000000   Code   RO         5264    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000228   0x08000228   0x00000002   Code   RO         5265    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800022a   0x0800022a   0x00000002   Code   RO         5293    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         5302    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         5304    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         5306    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         5309    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         5312    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         5314    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000000   Code   RO         5317    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x0800022c   0x0800022c   0x00000002   Code   RO         5318    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         5127    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800022e   0x0800022e   0x00000000   Code   RO         5143    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800022e   0x0800022e   0x00000006   Code   RO         5155    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000234   0x08000234   0x00000000   Code   RO         5145    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000234   0x08000234   0x00000004   Code   RO         5146    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000238   0x08000238   0x00000000   Code   RO         5148    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000238   0x08000238   0x00000008   Code   RO         5149    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000240   0x08000240   0x00000002   Code   RO         5266    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000242   0x08000242   0x00000000   Code   RO         5273    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000242   0x08000242   0x00000004   Code   RO         5274    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000246   0x08000246   0x00000006   Code   RO         5275    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800024c   0x0800024c   0x0000032c   Code   RO            3    .text               main.o
    0x08000578   0x08000578   0x00000030   Code   RO          163    .text               stm32f4xx_it.o
    0x080005a8   0x080005a8   0x00000150   Code   RO          219    .text               system_stm32f4xx.o
    0x080006f8   0x080006f8   0x00000040   Code   RO          252    .text               startup_stm32f40_41xxx.o
    0x08000738   0x08000738   0x000000ac   Code   RO          258    .text               misc.o
    0x080007e4   0x080007e4   0x000000de   Code   RO         1969    .text               stm32f4xx_gpio.o
    0x080008c2   0x080008c2   0x00000002   PAD
    0x080008c4   0x080008c4   0x0000012c   Code   RO         2837    .text               stm32f4xx_rcc.o
    0x080009f0   0x080009f0   0x0000036c   Code   RO         4130    .text               stm32f4xx_tim.o
    0x08000d5c   0x08000d5c   0x00000194   Code   RO         4606    .text               stm32f4xx_usart.o
    0x08000ef0   0x08000ef0   0x000000bc   Code   RO         4831    .text               delay.o
    0x08000fac   0x08000fac   0x00000148   Code   RO         4877    .text               usart.o
    0x080010f4   0x080010f4   0x000009e4   Code   RO         4908    .text               atd5984.o
    0x08001ad8   0x08001ad8   0x000003a0   Code   RO         5035    .text               motor_control.o
    0x08001e78   0x08001e78   0x00000002   Code   RO         5081    .text               c_w.l(use_no_semi_2.o)
    0x08001e7a   0x08001e7a   0x00000002   PAD
    0x08001e7c   0x08001e7c   0x00000018   Code   RO         5085    .text               c_w.l(noretval__2printf.o)
    0x08001e94   0x08001e94   0x00000052   Code   RO         5089    .text               c_w.l(_printf_str.o)
    0x08001ee6   0x08001ee6   0x00000002   PAD
    0x08001ee8   0x08001ee8   0x00000078   Code   RO         5091    .text               c_w.l(_printf_dec.o)
    0x08001f60   0x08001f60   0x0000010e   Code   RO         5101    .text               c_w.l(__printf_wp.o)
    0x0800206e   0x0800206e   0x00000006   Code   RO         5117    .text               c_w.l(heapauxi.o)
    0x08002074   0x08002074   0x00000002   Code   RO         5125    .text               c_w.l(use_no_semi.o)
    0x08002076   0x08002076   0x000000b2   Code   RO         5128    .text               c_w.l(_printf_intcommon.o)
    0x08002128   0x08002128   0x0000041e   Code   RO         5130    .text               c_w.l(_printf_fp_dec.o)
    0x08002546   0x08002546   0x0000002c   Code   RO         5132    .text               c_w.l(_printf_char.o)
    0x08002572   0x08002572   0x00000002   PAD
    0x08002574   0x08002574   0x00000024   Code   RO         5134    .text               c_w.l(_printf_char_file.o)
    0x08002598   0x08002598   0x00000008   Code   RO         5160    .text               c_w.l(rt_locale_intlibspace.o)
    0x080025a0   0x080025a0   0x0000008a   Code   RO         5162    .text               c_w.l(lludiv10.o)
    0x0800262a   0x0800262a   0x00000002   PAD
    0x0800262c   0x0800262c   0x00000030   Code   RO         5164    .text               c_w.l(_printf_char_common.o)
    0x0800265c   0x0800265c   0x00000080   Code   RO         5166    .text               c_w.l(_printf_fp_infnan.o)
    0x080026dc   0x080026dc   0x000000e4   Code   RO         5168    .text               c_w.l(bigflt0.o)
    0x080027c0   0x080027c0   0x00000008   Code   RO         5193    .text               c_w.l(ferror.o)
    0x080027c8   0x080027c8   0x00000008   Code   RO         5204    .text               c_w.l(libspace.o)
    0x080027d0   0x080027d0   0x0000004a   Code   RO         5207    .text               c_w.l(sys_stackheap_outer.o)
    0x0800281a   0x0800281a   0x00000012   Code   RO         5211    .text               c_w.l(exit.o)
    0x0800282c   0x0800282c   0x00000080   Code   RO         5213    .text               c_w.l(strcmpv7m.o)
    0x080028ac   0x080028ac   0x0000003e   Code   RO         5171    CL$$btod_d2e        c_w.l(btod.o)
    0x080028ea   0x080028ea   0x00000046   Code   RO         5173    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08002930   0x08002930   0x00000060   Code   RO         5172    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08002990   0x08002990   0x00000338   Code   RO         5181    CL$$btod_div_common  c_w.l(btod.o)
    0x08002cc8   0x08002cc8   0x000000dc   Code   RO         5178    CL$$btod_e2e        c_w.l(btod.o)
    0x08002da4   0x08002da4   0x0000002a   Code   RO         5175    CL$$btod_ediv       c_w.l(btod.o)
    0x08002dce   0x08002dce   0x0000002a   Code   RO         5174    CL$$btod_emul       c_w.l(btod.o)
    0x08002df8   0x08002df8   0x00000244   Code   RO         5180    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800303c   0x0800303c   0x00000030   Code   RO         5202    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800306c   0x0800306c   0x0000000e   Code   RO         5103    i._is_digit         c_w.l(__printf_wp.o)
    0x0800307a   0x0800307a   0x00000002   PAD
    0x0800307c   0x0800307c   0x0000002c   Code   RO         5198    locale$$code        c_w.l(lc_numeric_c.o)
    0x080030a8   0x080030a8   0x0000000c   Code   RO         5137    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x080030b4   0x080030b4   0x00000056   Code   RO         5121    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800310a   0x0800310a   0x0000008c   Code   RO         5139    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08003196   0x08003196   0x0000000a   Code   RO         5270    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080031a0   0x080031a0   0x00000004   Code   RO         5123    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080031a4   0x080031a4   0x00000000   Code   RO         5141    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080031a4   0x080031a4   0x00000094   Data   RO         5169    .constdata          c_w.l(bigflt0.o)
    0x08003238   0x08003238   0x00000020   Data   RO         5343    Region$$Table       anon$$obj.o
    0x08003258   0x08003258   0x0000001c   Data   RO         5197    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003274, Size: 0x00000748, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003274   0x00000010   Data   RW         2866    .data               stm32f4xx_rcc.o
    0x20000010   0x08003284   0x00000004   Data   RW         4833    .data               delay.o
    0x20000014   0x08003288   0x00000006   Data   RW         4879    .data               usart.o
    0x2000001a   0x0800328e   0x00000004   Data   RW         4912    .data               atd5984.o
    0x2000001e   0x08003292   0x00000002   Data   RW         5039    .data               motor_control.o
    0x20000020        -       0x000000c8   Zero   RW         4878    .bss                usart.o
    0x200000e8        -       0x00000060   Zero   RW         5205    .bss                c_w.l(libspace.o)
    0x20000148        -       0x00000200   Zero   RW          250    HEAP                startup_stm32f40_41xxx.o
    0x20000348        -       0x00000400   Zero   RW          249    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2532       1090          0          4          0       4333   atd5984.o
       188          8          0          4          0       1344   delay.o
       812        550          0          0          0     282299   main.o
       172         16          0          0          0       1481   misc.o
       928        492          0          2          0       4122   motor_control.o
        64         26        392          0       1536        848   startup_stm32f40_41xxx.o
       222          0          0          0          0       1853   stm32f4xx_gpio.o
        48          0          0          0          0       1482   stm32f4xx_it.o
       300         18          0         16          0       3306   stm32f4xx_rcc.o
       876         30          0          0          0       5732   stm32f4xx_tim.o
       404          8          0          0          0       2946   stm32f4xx_usart.o
       336         28          0          0          0       1161   system_stm32f4xx.o
       328         16          0          6        200       3378   usart.o

    ----------------------------------------------------------------------
      7212       <USER>        <GROUP>         32       1736     314285   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      5104        <USER>        <GROUP>          0         96       3532   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4792        204        176          0         96       2796   c_w.l
       252          8          0          0          0        612   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      5104        <USER>        <GROUP>          0         96       3532   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12316       2494        600         32       1832     313909   Grand Totals
     12316       2494        600         32       1832     313909   ELF Image Totals
     12316       2494        600         32          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                12916 (  12.61kB)
    Total RW  Size (RW Data + ZI Data)              1864 (   1.82kB)
    Total ROM Size (Code + RO Data + RW Data)      12948 (  12.64kB)

==============================================================================

