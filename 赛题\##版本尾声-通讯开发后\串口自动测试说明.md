# STM32串口自动测试程序

## 🚀 测试程序功能

我已经修改了main.c，添加了自动串口测试功能。STM32通电后会：

### 启动时发送（约3秒内）：
```
=== STM32F407 串口测试程序 ===
系统初始化完成
printf重定向测试成功
波特率: 115200
UART1: PA9(TX) PA10(RX)
开始循环测试...
```

### 循环测试（每3秒一次）：
```
第1轮 (3秒):
测试计数: 1, 运行时间: 3秒
START测试START指令

第2轮 (6秒):
测试计数: 2, 运行时间: 6秒
STOP测试STOP指令

第3轮 (9秒):
测试计数: 3, 运行时间: 9秒
left,50测试K230格式数据

第4轮 (12秒):
测试计数: 4, 运行时间: 12秒
right,30测试完成，循环重启
```

### 按键测试（随时可按）：
- 按K0按键会触发原有的控制逻辑
- 显示当前状态和发送相应指令

## 📊 示波器观察要点

### 启动阶段（0-4秒）：
- 连续的UART数据流
- 约6行文本，持续约1-2秒

### 循环阶段（每3秒）：
- **3秒时**：短脉冲"START" + 长脉冲中文说明
- **6秒时**：短脉冲"STOP" + 长脉冲中文说明  
- **9秒时**：中等脉冲"left,50" + 长脉冲中文说明
- **12秒时**：中等脉冲"right,30" + 长脉冲中文说明

### 波形特征：
- **115200波特率**：每位8.68μs
- **"START"（5字符）**：约435μs
- **"STOP"（4字符）**：约348μs  
- **"left,50"（7字符）**：约609μs

## 🔧 验证步骤

1. **编译程序**：
   ```
   打开Template.uvprojx → 编译(F7) → 烧录
   ```

2. **连接示波器**：
   ```
   示波器探头 → PA9 (STM32引脚)
   示波器地线 → GND
   ```

3. **示波器设置**：
   ```
   时间基准：500μs/div (观察单个指令)
   或 1s/div (观察整体时序)
   电压基准：1V/div
   触发：下降沿，1.5V
   ```

4. **上电测试**：
   - STM32复位或上电
   - 观察启动阶段的连续数据流
   - 观察每3秒的定时数据包

## ✅ 成功标志

**如果看到：**
- ✅ 上电后有连续的UART波形
- ✅ 每3秒有规律的数据包
- ✅ 按键时有即时响应波形
- **说明printf重定向完全正常！**

**如果没有波形：**
- ❌ 检查STM32供电和复位
- ❌ 检查PA9引脚连接
- ❌ 检查编译是否成功

这个测试程序可以完全独立验证串口功能，不需要K230配合。