#ifndef __LED_H
#define __LED_H

#include "sys.h"

// LED引脚定义 (根据常见开发板配置)
#define LED0_PIN        GPIO_Pin_13
#define LED0_PORT       GPIOF
#define LED0_CLK        RCC_AHB1Periph_GPIOF

// LED控制宏定义
#define LED0_ON()       GPIO_ResetBits(LED0_PORT, LED0_PIN)   // 低电平点亮
#define LED0_OFF()      GPIO_SetBits(LED0_PORT, LED0_PIN)     // 高电平熄灭

// 函数声明
void LED_Init(void);
void LED0_Toggle(void);
void LED0_Set(uint8_t state);

#endif