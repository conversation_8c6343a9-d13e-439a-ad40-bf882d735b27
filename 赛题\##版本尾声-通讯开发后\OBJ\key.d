..\obj\key.o: ..\HAREWARE\KEY\KEY.c
..\obj\key.o: ..\HAREWARE\KEY\../../SYSTEM/sys/sys.h
..\obj\key.o: ..\USER\stm32f4xx.h
..\obj\key.o: ..\CORE\core_cm4.h
..\obj\key.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\key.o: ..\CORE\core_cmInstr.h
..\obj\key.o: ..\CORE\core_cmFunc.h
..\obj\key.o: ..\CORE\core_cm4_simd.h
..\obj\key.o: ..\USER\system_stm32f4xx.h
..\obj\key.o: ..\USER\stm32f4xx_conf.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\key.o: ..\USER\stm32f4xx.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\key.o: ..\FWLIB\inc\misc.h
..\obj\key.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\key.o: ..\SYSTEM\delay\delay.h
..\obj\key.o: ..\SYSTEM\sys\sys.h
..\obj\key.o: ..\SYSTEM\usart\usart.h
..\obj\key.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
..\obj\key.o: ..\HAREWARE\ATD5984\ATD5984.h
..\obj\key.o: ..\HAREWARE\KEY\../../SYSTEM/sys/../../HAREWARE/MOTOR_CONTROL/motor_control.h
..\obj\key.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
..\obj\key.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
..\obj\key.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
..\obj\key.o: E:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
..\obj\key.o: ..\HAREWARE\KEY\KEY.h
