/**
 ******************************************************************************
 * @file    k230_comm.c
 * <AUTHOR>
 * @version V1.0
 * @date    2025-08-02
 * @brief   K230通信模块实现文件
 *          
 *          本模块实现与K230视觉模块的串口通信功能
 *          支持6字节控制指令协议解析和PID控制算法
 ******************************************************************************
 */

#include "../../SYSTEM/sys/sys.h"
#include "k230_comm.h"
#include "../../SYSTEM/delay/delay.h"
#include <string.h>
#include <stdio.h>
#include <math.h>

// 外部函数声明 - 由main.c提供
extern void Set_Aim_Completed_Flag(void);

/* 全局变量定义 -----------------------------------------------------------*/
K230_PID_t g_k230_pid;             /**< PID控制参数 */
K230_Control_t g_k230_control;     /**< 控制状态 */

/* 私有函数声明 -----------------------------------------------------------*/
static uint32_t get_tick_ms(void);
static uint16_t constrain_uint16(float value, uint16_t min_val, uint16_t max_val);
static float constrain_float(float value, float min_val, float max_val);

/* 公共函数实现 -----------------------------------------------------------*/

/**
 * @brief  K230通信模块初始化
 */
bool K230_Comm_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 初始化激光器GPIO (PC6)
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);
    GPIO_InitStructure.GPIO_Pin = LASER_GPIO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(LASER_GPIO_PORT, &GPIO_InitStructure);
    LASER_OFF();  // 初始关闭激光
    
    // 初始化PID参数
    g_k230_pid.kp = K230_DEFAULT_KP;
    g_k230_pid.ki = K230_DEFAULT_KI; 
    g_k230_pid.kd = K230_DEFAULT_KD;
    g_k230_pid.integral = 0.0f;
    g_k230_pid.last_error = 0.0f;
    g_k230_pid.max_integral = 100.0f;  // 积分限幅
    g_k230_pid.max_output = 300.0f;    // 输出限幅
    
    // 初始化控制状态
    g_k230_control.enabled = true;
    g_k230_control.last_cmd_time = get_tick_ms();
    g_k230_control.last_cmd = K230_CMD_STOP;
    g_k230_control.current_offset = 0;
    g_k230_control.pid_output = 0.0f;
    g_k230_control.timeout_ms = K230_TIMEOUT_MS;
    
    return true;
}

/**
 * @brief  PID参数设置
 */
void K230_PID_SetParams(float kp, float ki, float kd)
{
    g_k230_pid.kp = kp;
    g_k230_pid.ki = ki;
    g_k230_pid.kd = kd;
}

/**
 * @brief  PID控制计算
 */
float K230_PID_Calculate(float setpoint, float current_value)
{
    float error;
    float p_term;
    float i_term;
    float d_term;
    float output;
    
    error = setpoint - current_value;
    
    // 比例项
    p_term = g_k230_pid.kp * error;
    
    // 积分项
    g_k230_pid.integral += error;
    // 积分限幅
    g_k230_pid.integral = constrain_float(g_k230_pid.integral, 
                                         -g_k230_pid.max_integral, 
                                          g_k230_pid.max_integral);
    i_term = g_k230_pid.ki * g_k230_pid.integral;
    
    // 微分项
    d_term = g_k230_pid.kd * (error - g_k230_pid.last_error);
    g_k230_pid.last_error = error;
    
    // PID输出
    output = p_term + i_term + d_term;
    
    // 输出限幅
    output = constrain_float(output, -g_k230_pid.max_output, g_k230_pid.max_output);
    
    return output;
}

/**
 * @brief  PID积分清零
 */
void K230_PID_Reset(void)
{
    g_k230_pid.integral = 0.0f;
    g_k230_pid.last_error = 0.0f;
}

/**
 * @brief  像素偏差到电机速度转换
 */
uint16_t K230_Offset_To_Speed(int16_t pixel_offset)
{
    float pid_output;
    float abs_output;
    uint16_t speed;
    
    // 计算PID输出
    pid_output = K230_PID_Calculate(0.0f, (float)pixel_offset);
    g_k230_control.pid_output = pid_output;
    
    // 将PID输出映射到速度范围
    abs_output = (pid_output < 0) ? -pid_output : pid_output;
    
    // 线性映射: 0-300 -> 400-1500Hz
    speed = (uint16_t)(K230_MIN_SPEED + (abs_output / g_k230_pid.max_output) * (K230_MAX_SPEED - K230_MIN_SPEED));
    
    // 确保在有效范围内
    speed = constrain_uint16(speed, K230_MIN_SPEED, K230_MAX_SPEED);
    
    return speed;
}

/**
 * @brief  无标定角度映射函数
 */
float K230_NoCalib_Angle_Mapping(int16_t pixel_offset)
{
    float angle;
    int16_t abs_offset = (pixel_offset < 0) ? -pixel_offset : pixel_offset;
    
    // 理论计算：基于50cm观测距离和600像素分辨率
    // 经验公式：每100像素约对应4度 (可现场微调)
    angle = (float)abs_offset * 0.04f;
    
    // 最小角度限制 (避免过小的无效调整)
    if(angle < 0.1f) {
        angle = 0.1f;
    }
    
    // 最大角度限制 (避免过度旋转，E题第二问角度都很小)
    if(angle > 3.0f) {
        angle = 3.0f;
    }
    
    // 保持方向：负偏差左转，正偏差右转
    return (pixel_offset < 0) ? -angle : angle;
}

/**
 * @brief  执行控制指令
 */
bool K230_Execute_Command(uint8_t cmd, int16_t pixel_offset)
{
    if (!g_k230_control.enabled) {
        return false;
    }
    
    // 更新控制状态
    g_k230_control.last_cmd = cmd;
    g_k230_control.current_offset = pixel_offset;
    g_k230_control.last_cmd_time = get_tick_ms();
    
    switch (cmd) {
        case K230_CMD_LEFT:
        case K230_CMD_RIGHT:
            if (abs(pixel_offset) > K230_DEAD_ZONE) {
                // 1秒内单次精确调整策略
                // 使用最高速度和无标定角度映射
                float angle;
                uint16_t steps;
                uint16_t rotation_time;
                
                angle = K230_NoCalib_Angle_Mapping(pixel_offset);
                
                // 设置最高速度以满足1秒要求
                Motor_SetSpeed(MOTOR_AXIS_HORIZONTAL, K230_MAX_SPEED);  // 1500Hz最高速度
                
                // 单次精确旋转到计算角度
                Motor_Rotate(MOTOR_AXIS_HORIZONTAL, angle);
                
                // 预估旋转完成时间并等待(以硁步计算)
                steps = (uint16_t)(fabs(angle) * 8.889f);  // 8.889步/度
                rotation_time = (steps * 1000) / K230_MAX_SPEED + 50;  // 时间(ms) + 余量
                if(rotation_time > 200) rotation_time = 200;  // 最大等待200ms
                delay_ms(rotation_time);
                
                return true;
            }
            break;
            
        case K230_CMD_STOP:
            // 在死区内或明确停止指令
            Motor_Stop(MOTOR_AXIS_HORIZONTAL);
            K230_PID_Reset();  // 清除积分项
            return true;
            
        case K230_CMD_FIRE:
            // 发射指令 - 激光控制 (优化为100ms脆冲)
            LASER_ON();
            delay_ms(100);  // 激光持续100ms，节省时间
            LASER_OFF();
            return true;
            
        default:
            return false;
    }
    
    // 如果偏差在死区内，自动停止
    if (pixel_offset >= -K230_DEAD_ZONE && pixel_offset <= K230_DEAD_ZONE) {
        Motor_Stop(MOTOR_AXIS_HORIZONTAL);
    }
    
    return true;
}

/**
 * @brief  处理K230字符串数据
 */
bool K230_Process_Frame(void)
{
    char* data;
    char direction[16];
    int distance;
    int16_t offset;
    uint8_t cmd;
    
    if (!K230_IsStringReady()) {
        return false;
    }
    
    // 获取字符串数据
    data = K230_GetString();
    
    // 解析"NO"指令
    if(strncmp(data, "NO", 2) == 0) {
        Motor_Stop(MOTOR_AXIS_HORIZONTAL);
        K230_PID_Reset();
        g_k230_control.current_offset = 0;
        g_k230_control.last_cmd = K230_CMD_STOP;
        g_k230_control.last_cmd_time = get_tick_ms();
        K230_ClearStringFlag();
        return true;
    }
    
    // 解析"COMPLETED"指令 - 瞄准完成
    if(strncmp(data, "COMPLETED", 9) == 0) {
        Motor_Stop(MOTOR_AXIS_HORIZONTAL);
        K230_PID_Reset();
        g_k230_control.current_offset = 0;
        g_k230_control.last_cmd = K230_CMD_STOP;
        g_k230_control.last_cmd_time = get_tick_ms();
        // 调用main.c中的回调函数设置完成标志
        Set_Aim_Completed_Flag();
        K230_ClearStringFlag();
        return true;
    }
    
    // 解析"direction,distance"格式坐标
    memset(direction, 0, sizeof(direction));
    
    if(sscanf(data, "%15[^,],%d", direction, &distance) == 2) {
        // 根据方向生成控制指令
        if(strncmp(direction, "center", 6) == 0) {
            // 已对准中心，执行发射
            cmd = K230_CMD_FIRE;
            offset = 0;
        } else if(strncmp(direction, "left", 4) == 0) {
            cmd = K230_CMD_LEFT;
            offset = -distance;  // 负值表示左偏
        } else if(strncmp(direction, "right", 5) == 0) {
            cmd = K230_CMD_RIGHT;
            offset = distance;   // 正值表示右偏
        } else {
            cmd = K230_CMD_STOP;
            offset = 0;
        }
        
        // 更新控制状态
        g_k230_control.current_offset = offset;
        g_k230_control.last_cmd = cmd;
        g_k230_control.last_cmd_time = get_tick_ms();
        
        // 执行控制指令
        K230_Execute_Command(cmd, offset);
        
        K230_ClearStringFlag();
        return true;
    }
    
    // 无法解析的数据，清除标志
    K230_ClearStringFlag();
    return false;
}

/**
 * @brief  检查通信超时
 */
void K230_Check_Timeout(void)
{
    uint32_t current_time = get_tick_ms();
    
    if (current_time - g_k230_control.last_cmd_time > g_k230_control.timeout_ms) {
        // 超时，停止电机
        Motor_Stop(MOTOR_AXIS_HORIZONTAL);
        K230_PID_Reset();
        g_k230_control.enabled = false;
        
        // 延时后重新使能，等待新的命令
        delay_ms(100);
        g_k230_control.enabled = true;
        g_k230_control.last_cmd_time = current_time;
    }
}

/**
 * @brief  获取通信状态
 */
bool K230_Get_CommStatus(void)
{
    uint32_t current_time = get_tick_ms();
    return (current_time - g_k230_control.last_cmd_time) < g_k230_control.timeout_ms;
}

/**
 * @brief  获取最后接收到的偏差值
 */
int16_t K230_Get_LastOffset(void)
{
    return g_k230_control.current_offset;
}

/**
 * @brief  获取PID输出值
 */
float K230_Get_PIDOutput(void)
{
    return g_k230_control.pid_output;
}

/* 私有函数实现 -----------------------------------------------------------*/

/**
 * @brief  获取系统时间戳(毫秒)
 */
static uint32_t get_tick_ms(void)
{
    // 假设使用SysTick，1ms节拍
    // 这里需要根据实际的系统时钟实现
    // 简单实现：假设有全局毫秒计数器
    extern volatile uint32_t system_tick_ms;  // 需要在系统中定义
    return system_tick_ms;
}

/**
 * @brief  uint16_t类型数值限制
 */
static uint16_t constrain_uint16(float value, uint16_t min_val, uint16_t max_val)
{
    if (value < min_val) return min_val;
    if (value > max_val) return max_val;
    return (uint16_t)value;
}

/**
 * @brief  float类型数值限制
 */
static float constrain_float(float value, float min_val, float max_val)
{
    if (value < min_val) return min_val;
    if (value > max_val) return max_val;
    return value;
}
