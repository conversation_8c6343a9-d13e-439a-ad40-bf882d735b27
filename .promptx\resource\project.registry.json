{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-02T07:14:02.196Z", "updatedAt": "2025-08-02T07:14:02.204Z", "resourceCount": 9}, "resources": [{"id": "electronic-contest-advisor", "source": "project", "protocol": "role", "name": "Electronic Contest Advisor 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/electronic-contest-advisor/electronic-contest-advisor.role.md", "metadata": {"createdAt": "2025-08-02T07:14:02.198Z", "updatedAt": "2025-08-02T07:14:02.198Z", "scannedAt": "2025-08-02T07:14:02.198Z", "path": "role/electronic-contest-advisor/electronic-contest-advisor.role.md"}}, {"id": "advisory-workflow", "source": "project", "protocol": "execution", "name": "Advisory Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/electronic-contest-advisor/execution/advisory-workflow.execution.md", "metadata": {"createdAt": "2025-08-02T07:14:02.199Z", "updatedAt": "2025-08-02T07:14:02.199Z", "scannedAt": "2025-08-02T07:14:02.199Z", "path": "role/electronic-contest-advisor/execution/advisory-workflow.execution.md"}}, {"id": "contest-specific", "source": "project", "protocol": "thought", "name": "Contest Specific 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/electronic-contest-advisor/thought/contest-specific.thought.md", "metadata": {"createdAt": "2025-08-02T07:14:02.200Z", "updatedAt": "2025-08-02T07:14:02.200Z", "scannedAt": "2025-08-02T07:14:02.200Z", "path": "role/electronic-contest-advisor/thought/contest-specific.thought.md"}}, {"id": "k230-development-workflow", "source": "project", "protocol": "execution", "name": "K230 Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/k230-vision-expert/execution/k230-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-02T07:14:02.201Z", "updatedAt": "2025-08-02T07:14:02.201Z", "scannedAt": "2025-08-02T07:14:02.201Z", "path": "role/k230-vision-expert/execution/k230-development-workflow.execution.md"}}, {"id": "k230-vision-expert", "source": "project", "protocol": "role", "name": "K230 Vision Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/k230-vision-expert/k230-vision-expert.role.md", "metadata": {"createdAt": "2025-08-02T07:14:02.201Z", "updatedAt": "2025-08-02T07:14:02.201Z", "scannedAt": "2025-08-02T07:14:02.201Z", "path": "role/k230-vision-expert/k230-vision-expert.role.md"}}, {"id": "k230-vision-thinking", "source": "project", "protocol": "thought", "name": "K230 Vision Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/k230-vision-expert/thought/k230-vision-thinking.thought.md", "metadata": {"createdAt": "2025-08-02T07:14:02.202Z", "updatedAt": "2025-08-02T07:14:02.202Z", "scannedAt": "2025-08-02T07:14:02.202Z", "path": "role/k230-vision-expert/thought/k230-vision-thinking.thought.md"}}, {"id": "embedded-development", "source": "project", "protocol": "execution", "name": "Embedded Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stm32-hardware-expert/execution/embedded-development.execution.md", "metadata": {"createdAt": "2025-08-02T07:14:02.202Z", "updatedAt": "2025-08-02T07:14:02.202Z", "scannedAt": "2025-08-02T07:14:02.202Z", "path": "role/stm32-hardware-expert/execution/embedded-development.execution.md"}}, {"id": "stm32-hardware-expert", "source": "project", "protocol": "role", "name": "Stm32 Hardware Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/stm32-hardware-expert/stm32-hardware-expert.role.md", "metadata": {"createdAt": "2025-08-02T07:14:02.203Z", "updatedAt": "2025-08-02T07:14:02.203Z", "scannedAt": "2025-08-02T07:14:02.203Z", "path": "role/stm32-hardware-expert/stm32-hardware-expert.role.md"}}, {"id": "hardware-debugging", "source": "project", "protocol": "thought", "name": "Hardware Debugging 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stm32-hardware-expert/thought/hardware-debugging.thought.md", "metadata": {"createdAt": "2025-08-02T07:14:02.204Z", "updatedAt": "2025-08-02T07:14:02.204Z", "scannedAt": "2025-08-02T07:14:02.204Z", "path": "role/stm32-hardware-expert/thought/hardware-debugging.thought.md"}}], "stats": {"totalResources": 9, "byProtocol": {"role": 3, "execution": 3, "thought": 3}, "bySource": {"project": 9}}}