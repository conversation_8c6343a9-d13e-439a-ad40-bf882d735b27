{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-02T09:33:35.421Z", "updatedAt": "2025-08-02T09:33:35.475Z", "resourceCount": 9}, "resources": [{"id": "electronic-contest-advisor", "source": "project", "protocol": "role", "name": "Electronic Contest Advisor 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/electronic-contest-advisor/electronic-contest-advisor.role.md", "metadata": {"createdAt": "2025-08-02T09:33:35.429Z", "updatedAt": "2025-08-02T09:33:35.429Z", "scannedAt": "2025-08-02T09:33:35.429Z", "path": "role/electronic-contest-advisor/electronic-contest-advisor.role.md"}}, {"id": "advisory-workflow", "source": "project", "protocol": "execution", "name": "Advisory Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/electronic-contest-advisor/execution/advisory-workflow.execution.md", "metadata": {"createdAt": "2025-08-02T09:33:35.436Z", "updatedAt": "2025-08-02T09:33:35.436Z", "scannedAt": "2025-08-02T09:33:35.436Z", "path": "role/electronic-contest-advisor/execution/advisory-workflow.execution.md"}}, {"id": "contest-specific", "source": "project", "protocol": "thought", "name": "Contest Specific 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/electronic-contest-advisor/thought/contest-specific.thought.md", "metadata": {"createdAt": "2025-08-02T09:33:35.442Z", "updatedAt": "2025-08-02T09:33:35.442Z", "scannedAt": "2025-08-02T09:33:35.442Z", "path": "role/electronic-contest-advisor/thought/contest-specific.thought.md"}}, {"id": "k230-development-workflow", "source": "project", "protocol": "execution", "name": "K230 Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/k230-vision-expert/execution/k230-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-02T09:33:35.449Z", "updatedAt": "2025-08-02T09:33:35.449Z", "scannedAt": "2025-08-02T09:33:35.449Z", "path": "role/k230-vision-expert/execution/k230-development-workflow.execution.md"}}, {"id": "k230-vision-expert", "source": "project", "protocol": "role", "name": "K230 Vision Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/k230-vision-expert/k230-vision-expert.role.md", "metadata": {"createdAt": "2025-08-02T09:33:35.453Z", "updatedAt": "2025-08-02T09:33:35.453Z", "scannedAt": "2025-08-02T09:33:35.453Z", "path": "role/k230-vision-expert/k230-vision-expert.role.md"}}, {"id": "k230-vision-thinking", "source": "project", "protocol": "thought", "name": "K230 Vision Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/k230-vision-expert/thought/k230-vision-thinking.thought.md", "metadata": {"createdAt": "2025-08-02T09:33:35.459Z", "updatedAt": "2025-08-02T09:33:35.459Z", "scannedAt": "2025-08-02T09:33:35.459Z", "path": "role/k230-vision-expert/thought/k230-vision-thinking.thought.md"}}, {"id": "embedded-development", "source": "project", "protocol": "execution", "name": "Embedded Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/stm32-hardware-expert/execution/embedded-development.execution.md", "metadata": {"createdAt": "2025-08-02T09:33:35.464Z", "updatedAt": "2025-08-02T09:33:35.464Z", "scannedAt": "2025-08-02T09:33:35.464Z", "path": "role/stm32-hardware-expert/execution/embedded-development.execution.md"}}, {"id": "stm32-hardware-expert", "source": "project", "protocol": "role", "name": "Stm32 Hardware Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/stm32-hardware-expert/stm32-hardware-expert.role.md", "metadata": {"createdAt": "2025-08-02T09:33:35.469Z", "updatedAt": "2025-08-02T09:33:35.469Z", "scannedAt": "2025-08-02T09:33:35.469Z", "path": "role/stm32-hardware-expert/stm32-hardware-expert.role.md"}}, {"id": "hardware-debugging", "source": "project", "protocol": "thought", "name": "Hardware Debugging 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/stm32-hardware-expert/thought/hardware-debugging.thought.md", "metadata": {"createdAt": "2025-08-02T09:33:35.475Z", "updatedAt": "2025-08-02T09:33:35.475Z", "scannedAt": "2025-08-02T09:33:35.475Z", "path": "role/stm32-hardware-expert/thought/hardware-debugging.thought.md"}}], "stats": {"totalResources": 9, "byProtocol": {"role": 3, "execution": 3, "thought": 3}, "bySource": {"project": 9}}}