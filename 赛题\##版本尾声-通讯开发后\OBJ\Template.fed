;#<FEEDBACK># ARM Linker, 5060960: Last Updated: Sat Aug 02 15:02:17 2025
;VERSION 0.2
;FILE adc.o
ADC1_Init <= USED 0
Get_Adc1 <= USED 0
Get_adc_Average <= USED 0
__asm___5_adc_c_c5b3b85d____REV16 <= USED 0
__asm___5_adc_c_c5b3b85d____REVSH <= USED 0
;FILE atd5984.o
Motor_A_DirectionTest <= USED 0
Motor_A_StepsCalibration <= USED 0
TIM1_GetCurrentFrequency <= USED 0
TIM8_GetCurrentFrequency <= USED 0
__asm___9_ATD5984_c_88279b85____REV16 <= USED 0
__asm___9_ATD5984_c_88279b85____REVSH <= USED 0
;FILE delay.o
__asm___7_delay_c_f6a9c549____REV16 <= USED 0
__asm___7_delay_c_f6a9c549____REVSH <= USED 0
delay_us <= USED 0
;FILE k230_comm.o
K230_Get_CommStatus <= USED 0
K230_Get_LastOffset <= USED 0
K230_Get_PIDOutput <= USED 0
K230_Offset_To_Speed <= USED 0
K230_PID_Calculate <= USED 0
K230_PID_SetParams <= USED 0
__asm___11_k230_comm_c_f643efaa____REV16 <= USED 0
__asm___11_k230_comm_c_f643efaa____REVSH <= USED 0
;FILE key.o
Key_Scan <= USED 0
__asm___5_KEY_c_Key_Init____REV16 <= USED 0
__asm___5_KEY_c_Key_Init____REVSH <= USED 0
;FILE main.o
__asm___6_main_c_0ac92839____REV16 <= USED 0
__asm___6_main_c_0ac92839____REVSH <= USED 0
;FILE misc.o
NVIC_SetVectorTable <= USED 0
NVIC_SystemLPConfig <= USED 0
__asm___6_misc_c_d0fc1254____REV16 <= USED 0
__asm___6_misc_c_d0fc1254____REVSH <= USED 0
;FILE motor_control.o
Motor_Emergency_Stop <= USED 0
Motor_GetSpeed <= USED 0
Motor_GetState <= USED 0
Motor_IsMoving <= USED 0
__asm___15_motor_control_c_690393c3____REV16 <= USED 0
__asm___15_motor_control_c_690393c3____REVSH <= USED 0
;FILE startup_stm32f40_41xxx.o
;FILE stm32f4xx_adc.o
ADC_AnalogWatchdogCmd <= USED 0
ADC_AnalogWatchdogSingleChannelConfig <= USED 0
ADC_AnalogWatchdogThresholdsConfig <= USED 0
ADC_AutoInjectedConvCmd <= USED 0
ADC_ClearFlag <= USED 0
ADC_ClearITPendingBit <= USED 0
ADC_Cmd <= USED 0
ADC_CommonInit <= USED 0
ADC_CommonStructInit <= USED 0
ADC_ContinuousModeCmd <= USED 0
ADC_DMACmd <= USED 0
ADC_DMARequestAfterLastTransferCmd <= USED 0
ADC_DeInit <= USED 0
ADC_DiscModeChannelCountConfig <= USED 0
ADC_DiscModeCmd <= USED 0
ADC_EOCOnEachRegularChannelCmd <= USED 0
ADC_ExternalTrigInjectedConvConfig <= USED 0
ADC_ExternalTrigInjectedConvEdgeConfig <= USED 0
ADC_GetConversionValue <= USED 0
ADC_GetFlagStatus <= USED 0
ADC_GetITStatus <= USED 0
ADC_GetInjectedConversionValue <= USED 0
ADC_GetMultiModeConversionValue <= USED 0
ADC_GetSoftwareStartConvStatus <= USED 0
ADC_GetSoftwareStartInjectedConvCmdStatus <= USED 0
ADC_ITConfig <= USED 0
ADC_Init <= USED 0
ADC_InjectedChannelConfig <= USED 0
ADC_InjectedDiscModeCmd <= USED 0
ADC_InjectedSequencerLengthConfig <= USED 0
ADC_MultiModeDMARequestAfterLastTransferCmd <= USED 0
ADC_RegularChannelConfig <= USED 0
ADC_SetInjectedOffset <= USED 0
ADC_SoftwareStartConv <= USED 0
ADC_SoftwareStartInjectedConv <= USED 0
ADC_StructInit <= USED 0
ADC_TempSensorVrefintCmd <= USED 0
ADC_VBATCmd <= USED 0
__asm___15_stm32f4xx_adc_c_e11a2ea2____REV16 <= USED 0
__asm___15_stm32f4xx_adc_c_e11a2ea2____REVSH <= USED 0
;FILE stm32f4xx_can.o
CAN_CancelTransmit <= USED 0
CAN_ClearFlag <= USED 0
CAN_ClearITPendingBit <= USED 0
CAN_DBGFreeze <= USED 0
CAN_DeInit <= USED 0
CAN_FIFORelease <= USED 0
CAN_FilterInit <= USED 0
CAN_GetFlagStatus <= USED 0
CAN_GetITStatus <= USED 0
CAN_GetLSBTransmitErrorCounter <= USED 0
CAN_GetLastErrorCode <= USED 0
CAN_GetReceiveErrorCounter <= USED 0
CAN_ITConfig <= USED 0
CAN_Init <= USED 0
CAN_MessagePending <= USED 0
CAN_OperatingModeRequest <= USED 0
CAN_Receive <= USED 0
CAN_SlaveStartBank <= USED 0
CAN_Sleep <= USED 0
CAN_StructInit <= USED 0
CAN_TTComModeCmd <= USED 0
CAN_Transmit <= USED 0
CAN_TransmitStatus <= USED 0
CAN_WakeUp <= USED 0
__asm___15_stm32f4xx_can_c_347dae01____REV16 <= USED 0
__asm___15_stm32f4xx_can_c_347dae01____REVSH <= USED 0
;FILE stm32f4xx_crc.o
CRC_CalcBlockCRC <= USED 0
CRC_CalcCRC <= USED 0
CRC_GetCRC <= USED 0
CRC_GetIDRegister <= USED 0
CRC_ResetDR <= USED 0
CRC_SetIDRegister <= USED 0
__asm___15_stm32f4xx_crc_c_eea4c7d3____REV16 <= USED 0
__asm___15_stm32f4xx_crc_c_eea4c7d3____REVSH <= USED 0
;FILE stm32f4xx_cryp.o
CRYP_Cmd <= USED 0
CRYP_DMACmd <= USED 0
CRYP_DataIn <= USED 0
CRYP_DataOut <= USED 0
CRYP_DeInit <= USED 0
CRYP_FIFOFlush <= USED 0
CRYP_GetCmdStatus <= USED 0
CRYP_GetFlagStatus <= USED 0
CRYP_GetITStatus <= USED 0
CRYP_ITConfig <= USED 0
CRYP_IVInit <= USED 0
CRYP_IVStructInit <= USED 0
CRYP_Init <= USED 0
CRYP_KeyInit <= USED 0
CRYP_KeyStructInit <= USED 0
CRYP_PhaseConfig <= USED 0
CRYP_RestoreContext <= USED 0
CRYP_SaveContext <= USED 0
CRYP_StructInit <= USED 0
__asm___16_stm32f4xx_cryp_c_459f21d3____REV16 <= USED 0
__asm___16_stm32f4xx_cryp_c_459f21d3____REVSH <= USED 0
;FILE stm32f4xx_cryp_aes.o
CRYP_AES_CBC <= USED 0
CRYP_AES_CCM <= USED 0
CRYP_AES_CTR <= USED 0
CRYP_AES_ECB <= USED 0
CRYP_AES_GCM <= USED 0
__asm___20_stm32f4xx_cryp_aes_c_c34cf6b8____REV16 <= USED 0
__asm___20_stm32f4xx_cryp_aes_c_c34cf6b8____REVSH <= USED 0
;FILE stm32f4xx_cryp_des.o
CRYP_DES_CBC <= USED 0
CRYP_DES_ECB <= USED 0
__asm___20_stm32f4xx_cryp_des_c_9174d91f____REV16 <= USED 0
__asm___20_stm32f4xx_cryp_des_c_9174d91f____REVSH <= USED 0
;FILE stm32f4xx_cryp_tdes.o
CRYP_TDES_CBC <= USED 0
CRYP_TDES_ECB <= USED 0
__asm___21_stm32f4xx_cryp_tdes_c_2fc87a0d____REV16 <= USED 0
__asm___21_stm32f4xx_cryp_tdes_c_2fc87a0d____REVSH <= USED 0
;FILE stm32f4xx_dac.o
DAC_ClearFlag <= USED 0
DAC_ClearITPendingBit <= USED 0
DAC_Cmd <= USED 0
DAC_DMACmd <= USED 0
DAC_DeInit <= USED 0
DAC_DualSoftwareTriggerCmd <= USED 0
DAC_GetDataOutputValue <= USED 0
DAC_GetFlagStatus <= USED 0
DAC_GetITStatus <= USED 0
DAC_ITConfig <= USED 0
DAC_Init <= USED 0
DAC_SetChannel1Data <= USED 0
DAC_SetChannel2Data <= USED 0
DAC_SetDualChannelData <= USED 0
DAC_SoftwareTriggerCmd <= USED 0
DAC_StructInit <= USED 0
DAC_WaveGenerationCmd <= USED 0
__asm___15_stm32f4xx_dac_c_4da4a0a9____REV16 <= USED 0
__asm___15_stm32f4xx_dac_c_4da4a0a9____REVSH <= USED 0
;FILE stm32f4xx_dbgmcu.o
DBGMCU_APB1PeriphConfig <= USED 0
DBGMCU_APB2PeriphConfig <= USED 0
DBGMCU_Config <= USED 0
DBGMCU_GetDEVID <= USED 0
DBGMCU_GetREVID <= USED 0
__asm___18_stm32f4xx_dbgmcu_c_588a430f____REV16 <= USED 0
__asm___18_stm32f4xx_dbgmcu_c_588a430f____REVSH <= USED 0
;FILE stm32f4xx_dcmi.o
DCMI_CROPCmd <= USED 0
DCMI_CROPConfig <= USED 0
DCMI_CaptureCmd <= USED 0
DCMI_ClearFlag <= USED 0
DCMI_ClearITPendingBit <= USED 0
DCMI_Cmd <= USED 0
DCMI_DeInit <= USED 0
DCMI_GetFlagStatus <= USED 0
DCMI_GetITStatus <= USED 0
DCMI_ITConfig <= USED 0
DCMI_Init <= USED 0
DCMI_JPEGCmd <= USED 0
DCMI_ReadData <= USED 0
DCMI_SetEmbeddedSynchroCodes <= USED 0
DCMI_StructInit <= USED 0
__asm___16_stm32f4xx_dcmi_c_3610e7fb____REV16 <= USED 0
__asm___16_stm32f4xx_dcmi_c_3610e7fb____REVSH <= USED 0
;FILE stm32f4xx_dma.o
DMA_ClearFlag <= USED 0
DMA_ClearITPendingBit <= USED 0
DMA_Cmd <= USED 0
DMA_DeInit <= USED 0
DMA_DoubleBufferModeCmd <= USED 0
DMA_DoubleBufferModeConfig <= USED 0
DMA_FlowControllerConfig <= USED 0
DMA_GetCmdStatus <= USED 0
DMA_GetCurrDataCounter <= USED 0
DMA_GetCurrentMemoryTarget <= USED 0
DMA_GetFIFOStatus <= USED 0
DMA_GetFlagStatus <= USED 0
DMA_GetITStatus <= USED 0
DMA_ITConfig <= USED 0
DMA_Init <= USED 0
DMA_MemoryTargetConfig <= USED 0
DMA_PeriphIncOffsetSizeConfig <= USED 0
DMA_SetCurrDataCounter <= USED 0
DMA_StructInit <= USED 0
__asm___15_stm32f4xx_dma_c_e9b554c0____REV16 <= USED 0
__asm___15_stm32f4xx_dma_c_e9b554c0____REVSH <= USED 0
;FILE stm32f4xx_dma2d.o
DMA2D_AbortTransfer <= USED 0
DMA2D_BGConfig <= USED 0
DMA2D_BGStart <= USED 0
DMA2D_BG_StructInit <= USED 0
DMA2D_ClearFlag <= USED 0
DMA2D_ClearITPendingBit <= USED 0
DMA2D_DeInit <= USED 0
DMA2D_DeadTimeConfig <= USED 0
DMA2D_FGConfig <= USED 0
DMA2D_FGStart <= USED 0
DMA2D_FG_StructInit <= USED 0
DMA2D_GetFlagStatus <= USED 0
DMA2D_GetITStatus <= USED 0
DMA2D_ITConfig <= USED 0
DMA2D_Init <= USED 0
DMA2D_LineWatermarkConfig <= USED 0
DMA2D_StartTransfer <= USED 0
DMA2D_StructInit <= USED 0
DMA2D_Suspend <= USED 0
__asm___17_stm32f4xx_dma2d_c_de80eb9a____REV16 <= USED 0
__asm___17_stm32f4xx_dma2d_c_de80eb9a____REVSH <= USED 0
;FILE stm32f4xx_exti.o
EXTI_ClearFlag <= USED 0
EXTI_ClearITPendingBit <= USED 0
EXTI_DeInit <= USED 0
EXTI_GenerateSWInterrupt <= USED 0
EXTI_GetFlagStatus <= USED 0
EXTI_GetITStatus <= USED 0
EXTI_Init <= USED 0
EXTI_StructInit <= USED 0
__asm___16_stm32f4xx_exti_c_28b3b60e____REV16 <= USED 0
__asm___16_stm32f4xx_exti_c_28b3b60e____REVSH <= USED 0
;FILE stm32f4xx_flash.o
FLASH_ClearFlag <= USED 0
FLASH_DataCacheCmd <= USED 0
FLASH_DataCacheReset <= USED 0
FLASH_EraseAllBank1Sectors <= USED 0
FLASH_EraseAllBank2Sectors <= USED 0
FLASH_EraseAllSectors <= USED 0
FLASH_EraseSector <= USED 0
FLASH_GetFlagStatus <= USED 0
FLASH_GetStatus <= USED 0
FLASH_ITConfig <= USED 0
FLASH_InstructionCacheCmd <= USED 0
FLASH_InstructionCacheReset <= USED 0
FLASH_Lock <= USED 0
FLASH_OB_BORConfig <= USED 0
FLASH_OB_BootConfig <= USED 0
FLASH_OB_GetBOR <= USED 0
FLASH_OB_GetPCROP <= USED 0
FLASH_OB_GetPCROP1 <= USED 0
FLASH_OB_GetRDP <= USED 0
FLASH_OB_GetUser <= USED 0
FLASH_OB_GetWRP <= USED 0
FLASH_OB_GetWRP1 <= USED 0
FLASH_OB_Launch <= USED 0
FLASH_OB_Lock <= USED 0
FLASH_OB_PCROP1Config <= USED 0
FLASH_OB_PCROPConfig <= USED 0
FLASH_OB_PCROPSelectionConfig <= USED 0
FLASH_OB_RDPConfig <= USED 0
FLASH_OB_Unlock <= USED 0
FLASH_OB_UserConfig <= USED 0
FLASH_OB_WRP1Config <= USED 0
FLASH_OB_WRPConfig <= USED 0
FLASH_PrefetchBufferCmd <= USED 0
FLASH_ProgramByte <= USED 0
FLASH_ProgramDoubleWord <= USED 0
FLASH_ProgramHalfWord <= USED 0
FLASH_ProgramWord <= USED 0
FLASH_SetLatency <= USED 0
FLASH_Unlock <= USED 0
FLASH_WaitForLastOperation <= USED 0
__asm___17_stm32f4xx_flash_c_a2a150d6____REV16 <= USED 0
__asm___17_stm32f4xx_flash_c_a2a150d6____REVSH <= USED 0
;FILE stm32f4xx_flash_ramfunc.o
FLASH_FlashInterfaceCmd <= USED 0
FLASH_FlashSleepModeCmd <= USED 0
__asm___25_stm32f4xx_flash_ramfunc_c_66cdcef2____REV16 <= USED 0
__asm___25_stm32f4xx_flash_ramfunc_c_66cdcef2____REVSH <= USED 0
;FILE stm32f4xx_fsmc.o
FSMC_ClearFlag <= USED 0
FSMC_ClearITPendingBit <= USED 0
FSMC_GetECC <= USED 0
FSMC_GetFlagStatus <= USED 0
FSMC_GetITStatus <= USED 0
FSMC_ITConfig <= USED 0
FSMC_NANDCmd <= USED 0
FSMC_NANDDeInit <= USED 0
FSMC_NANDECCCmd <= USED 0
FSMC_NANDInit <= USED 0
FSMC_NANDStructInit <= USED 0
FSMC_NORSRAMCmd <= USED 0
FSMC_NORSRAMDeInit <= USED 0
FSMC_NORSRAMInit <= USED 0
FSMC_NORSRAMStructInit <= USED 0
FSMC_PCCARDCmd <= USED 0
FSMC_PCCARDDeInit <= USED 0
FSMC_PCCARDInit <= USED 0
FSMC_PCCARDStructInit <= USED 0
__asm___16_stm32f4xx_fsmc_c_13019877____REV16 <= USED 0
__asm___16_stm32f4xx_fsmc_c_13019877____REVSH <= USED 0
;FILE stm32f4xx_gpio.o
GPIO_DeInit <= USED 0
GPIO_PinLockConfig <= USED 0
GPIO_ReadInputData <= USED 0
GPIO_ReadInputDataBit <= USED 0
GPIO_ReadOutputData <= USED 0
GPIO_ReadOutputDataBit <= USED 0
GPIO_StructInit <= USED 0
GPIO_ToggleBits <= USED 0
GPIO_Write <= USED 0
GPIO_WriteBit <= USED 0
__asm___16_stm32f4xx_gpio_c_f8e8e39a____REV16 <= USED 0
__asm___16_stm32f4xx_gpio_c_f8e8e39a____REVSH <= USED 0
;FILE stm32f4xx_hash.o
HASH_AutoStartDigest <= USED 0
HASH_ClearFlag <= USED 0
HASH_ClearITPendingBit <= USED 0
HASH_DMACmd <= USED 0
HASH_DataIn <= USED 0
HASH_DeInit <= USED 0
HASH_GetDigest <= USED 0
HASH_GetFlagStatus <= USED 0
HASH_GetITStatus <= USED 0
HASH_GetInFIFOWordsNbr <= USED 0
HASH_ITConfig <= USED 0
HASH_Init <= USED 0
HASH_Reset <= USED 0
HASH_RestoreContext <= USED 0
HASH_SaveContext <= USED 0
HASH_SetLastWordValidBitsNbr <= USED 0
HASH_StartDigest <= USED 0
HASH_StructInit <= USED 0
__asm___16_stm32f4xx_hash_c_90573c7c____REV16 <= USED 0
__asm___16_stm32f4xx_hash_c_90573c7c____REVSH <= USED 0
;FILE stm32f4xx_hash_md5.o
HASH_MD5 <= USED 0
HMAC_MD5 <= USED 0
__asm___20_stm32f4xx_hash_md5_c_HASH_MD5____REV16 <= USED 0
__asm___20_stm32f4xx_hash_md5_c_HASH_MD5____REVSH <= USED 0
;FILE stm32f4xx_hash_sha1.o
HASH_SHA1 <= USED 0
HMAC_SHA1 <= USED 0
__asm___21_stm32f4xx_hash_sha1_c_328c56b9____REV16 <= USED 0
__asm___21_stm32f4xx_hash_sha1_c_328c56b9____REVSH <= USED 0
;FILE stm32f4xx_i2c.o
I2C_ARPCmd <= USED 0
I2C_AcknowledgeConfig <= USED 0
I2C_AnalogFilterCmd <= USED 0
I2C_CalculatePEC <= USED 0
I2C_CheckEvent <= USED 0
I2C_ClearFlag <= USED 0
I2C_ClearITPendingBit <= USED 0
I2C_Cmd <= USED 0
I2C_DMACmd <= USED 0
I2C_DMALastTransferCmd <= USED 0
I2C_DeInit <= USED 0
I2C_DigitalFilterConfig <= USED 0
I2C_DualAddressCmd <= USED 0
I2C_FastModeDutyCycleConfig <= USED 0
I2C_GeneralCallCmd <= USED 0
I2C_GenerateSTART <= USED 0
I2C_GenerateSTOP <= USED 0
I2C_GetFlagStatus <= USED 0
I2C_GetITStatus <= USED 0
I2C_GetLastEvent <= USED 0
I2C_GetPEC <= USED 0
I2C_ITConfig <= USED 0
I2C_Init <= USED 0
I2C_NACKPositionConfig <= USED 0
I2C_OwnAddress2Config <= USED 0
I2C_PECPositionConfig <= USED 0
I2C_ReadRegister <= USED 0
I2C_ReceiveData <= USED 0
I2C_SMBusAlertConfig <= USED 0
I2C_Send7bitAddress <= USED 0
I2C_SendData <= USED 0
I2C_SoftwareResetCmd <= USED 0
I2C_StretchClockCmd <= USED 0
I2C_StructInit <= USED 0
I2C_TransmitPEC <= USED 0
__asm___15_stm32f4xx_i2c_c_7174d409____REV16 <= USED 0
__asm___15_stm32f4xx_i2c_c_7174d409____REVSH <= USED 0
;FILE stm32f4xx_it.o
__asm___14_stm32f4xx_it_c_bb8ca80c____REV16 <= USED 0
__asm___14_stm32f4xx_it_c_bb8ca80c____REVSH <= USED 0
;FILE stm32f4xx_iwdg.o
IWDG_Enable <= USED 0
IWDG_GetFlagStatus <= USED 0
IWDG_ReloadCounter <= USED 0
IWDG_SetPrescaler <= USED 0
IWDG_SetReload <= USED 0
IWDG_WriteAccessCmd <= USED 0
__asm___16_stm32f4xx_iwdg_c_15798b01____REV16 <= USED 0
__asm___16_stm32f4xx_iwdg_c_15798b01____REVSH <= USED 0
;FILE stm32f4xx_ltdc.o
LTDC_CLUTCmd <= USED 0
LTDC_CLUTInit <= USED 0
LTDC_CLUTStructInit <= USED 0
LTDC_ClearFlag <= USED 0
LTDC_ClearITPendingBit <= USED 0
LTDC_Cmd <= USED 0
LTDC_ColorKeyingConfig <= USED 0
LTDC_ColorKeyingStructInit <= USED 0
LTDC_DeInit <= USED 0
LTDC_DitherCmd <= USED 0
LTDC_GetCDStatus <= USED 0
LTDC_GetFlagStatus <= USED 0
LTDC_GetITStatus <= USED 0
LTDC_GetPosStatus <= USED 0
LTDC_GetRGBWidth <= USED 0
LTDC_ITConfig <= USED 0
LTDC_Init <= USED 0
LTDC_LIPConfig <= USED 0
LTDC_LayerAddress <= USED 0
LTDC_LayerAlpha <= USED 0
LTDC_LayerCmd <= USED 0
LTDC_LayerInit <= USED 0
LTDC_LayerPixelFormat <= USED 0
LTDC_LayerPosition <= USED 0
LTDC_LayerSize <= USED 0
LTDC_LayerStructInit <= USED 0
LTDC_PosStructInit <= USED 0
LTDC_RGBStructInit <= USED 0
LTDC_ReloadConfig <= USED 0
LTDC_StructInit <= USED 0
__asm___16_stm32f4xx_ltdc_c_74c236bc____REV16 <= USED 0
__asm___16_stm32f4xx_ltdc_c_74c236bc____REVSH <= USED 0
;FILE stm32f4xx_pwr.o
PWR_BackupAccessCmd <= USED 0
PWR_BackupRegulatorCmd <= USED 0
PWR_ClearFlag <= USED 0
PWR_DeInit <= USED 0
PWR_EnterSTANDBYMode <= USED 0
PWR_EnterSTOPMode <= USED 0
PWR_EnterUnderDriveSTOPMode <= USED 0
PWR_FlashPowerDownCmd <= USED 0
PWR_GetFlagStatus <= USED 0
PWR_LowRegulatorLowVoltageCmd <= USED 0
PWR_MainRegulatorLowVoltageCmd <= USED 0
PWR_MainRegulatorModeConfig <= USED 0
PWR_OverDriveCmd <= USED 0
PWR_OverDriveSWCmd <= USED 0
PWR_PVDCmd <= USED 0
PWR_PVDLevelConfig <= USED 0
PWR_UnderDriveCmd <= USED 0
PWR_WakeUpPinCmd <= USED 0
__asm___15_stm32f4xx_pwr_c_0c2a8b75____REV16 <= USED 0
__asm___15_stm32f4xx_pwr_c_0c2a8b75____REVSH <= USED 0
;FILE stm32f4xx_rcc.o
RCC_AHB1PeriphClockLPModeCmd <= USED 0
RCC_AHB1PeriphResetCmd <= USED 0
RCC_AHB2PeriphClockCmd <= USED 0
RCC_AHB2PeriphClockLPModeCmd <= USED 0
RCC_AHB2PeriphResetCmd <= USED 0
RCC_AHB3PeriphClockCmd <= USED 0
RCC_AHB3PeriphClockLPModeCmd <= USED 0
RCC_AHB3PeriphResetCmd <= USED 0
RCC_APB1PeriphClockCmd <= USED 0
RCC_APB1PeriphClockLPModeCmd <= USED 0
RCC_APB1PeriphResetCmd <= USED 0
RCC_APB2PeriphClockLPModeCmd <= USED 0
RCC_APB2PeriphResetCmd <= USED 0
RCC_AdjustHSICalibrationValue <= USED 0
RCC_BackupResetCmd <= USED 0
RCC_ClearFlag <= USED 0
RCC_ClearITPendingBit <= USED 0
RCC_ClockSecuritySystemCmd <= USED 0
RCC_DeInit <= USED 0
RCC_GetFlagStatus <= USED 0
RCC_GetITStatus <= USED 0
RCC_GetSYSCLKSource <= USED 0
RCC_HCLKConfig <= USED 0
RCC_HSEConfig <= USED 0
RCC_HSICmd <= USED 0
RCC_I2SCLKConfig <= USED 0
RCC_ITConfig <= USED 0
RCC_LSEConfig <= USED 0
RCC_LSEModeConfig <= USED 0
RCC_LSICmd <= USED 0
RCC_LTDCCLKDivConfig <= USED 0
RCC_MCO1Config <= USED 0
RCC_MCO2Config <= USED 0
RCC_PCLK1Config <= USED 0
RCC_PCLK2Config <= USED 0
RCC_PLLCmd <= USED 0
RCC_PLLConfig <= USED 0
RCC_PLLI2SCmd <= USED 0
RCC_PLLI2SConfig <= USED 0
RCC_PLLSAICmd <= USED 0
RCC_PLLSAIConfig <= USED 0
RCC_RTCCLKCmd <= USED 0
RCC_RTCCLKConfig <= USED 0
RCC_SAIBlockACLKConfig <= USED 0
RCC_SAIBlockBCLKConfig <= USED 0
RCC_SAIPLLI2SClkDivConfig <= USED 0
RCC_SAIPLLSAIClkDivConfig <= USED 0
RCC_SYSCLKConfig <= USED 0
RCC_TIMCLKPresConfig <= USED 0
RCC_WaitForHSEStartUp <= USED 0
__asm___15_stm32f4xx_rcc_c_49e27980____REV16 <= USED 0
__asm___15_stm32f4xx_rcc_c_49e27980____REVSH <= USED 0
;FILE stm32f4xx_rng.o
RNG_ClearFlag <= USED 0
RNG_ClearITPendingBit <= USED 0
RNG_Cmd <= USED 0
RNG_DeInit <= USED 0
RNG_GetFlagStatus <= USED 0
RNG_GetITStatus <= USED 0
RNG_GetRandomNumber <= USED 0
RNG_ITConfig <= USED 0
__asm___15_stm32f4xx_rng_c_3ce7902d____REV16 <= USED 0
__asm___15_stm32f4xx_rng_c_3ce7902d____REVSH <= USED 0
;FILE stm32f4xx_rtc.o
RTC_AlarmCmd <= USED 0
RTC_AlarmStructInit <= USED 0
RTC_AlarmSubSecondConfig <= USED 0
RTC_BypassShadowCmd <= USED 0
RTC_CalibOutputCmd <= USED 0
RTC_CalibOutputConfig <= USED 0
RTC_ClearFlag <= USED 0
RTC_ClearITPendingBit <= USED 0
RTC_CoarseCalibCmd <= USED 0
RTC_CoarseCalibConfig <= USED 0
RTC_DateStructInit <= USED 0
RTC_DayLightSavingConfig <= USED 0
RTC_DeInit <= USED 0
RTC_EnterInitMode <= USED 0
RTC_ExitInitMode <= USED 0
RTC_GetAlarm <= USED 0
RTC_GetAlarmSubSecond <= USED 0
RTC_GetDate <= USED 0
RTC_GetFlagStatus <= USED 0
RTC_GetITStatus <= USED 0
RTC_GetStoreOperation <= USED 0
RTC_GetSubSecond <= USED 0
RTC_GetTime <= USED 0
RTC_GetTimeStamp <= USED 0
RTC_GetTimeStampSubSecond <= USED 0
RTC_GetWakeUpCounter <= USED 0
RTC_ITConfig <= USED 0
RTC_Init <= USED 0
RTC_OutputConfig <= USED 0
RTC_OutputTypeConfig <= USED 0
RTC_ReadBackupRegister <= USED 0
RTC_RefClockCmd <= USED 0
RTC_SetAlarm <= USED 0
RTC_SetDate <= USED 0
RTC_SetTime <= USED 0
RTC_SetWakeUpCounter <= USED 0
RTC_SmoothCalibConfig <= USED 0
RTC_StructInit <= USED 0
RTC_SynchroShiftConfig <= USED 0
RTC_TamperCmd <= USED 0
RTC_TamperFilterConfig <= USED 0
RTC_TamperPinSelection <= USED 0
RTC_TamperPinsPrechargeDuration <= USED 0
RTC_TamperPullUpCmd <= USED 0
RTC_TamperSamplingFreqConfig <= USED 0
RTC_TamperTriggerConfig <= USED 0
RTC_TimeStampCmd <= USED 0
RTC_TimeStampOnTamperDetectionCmd <= USED 0
RTC_TimeStampPinSelection <= USED 0
RTC_TimeStructInit <= USED 0
RTC_WaitForSynchro <= USED 0
RTC_WakeUpClockConfig <= USED 0
RTC_WakeUpCmd <= USED 0
RTC_WriteBackupRegister <= USED 0
RTC_WriteProtectionCmd <= USED 0
__asm___15_stm32f4xx_rtc_c_81435638____REV16 <= USED 0
__asm___15_stm32f4xx_rtc_c_81435638____REVSH <= USED 0
;FILE stm32f4xx_sai.o
SAI_ClearFlag <= USED 0
SAI_ClearITPendingBit <= USED 0
SAI_Cmd <= USED 0
SAI_CompandingModeConfig <= USED 0
SAI_DMACmd <= USED 0
SAI_DeInit <= USED 0
SAI_FlushFIFO <= USED 0
SAI_FrameInit <= USED 0
SAI_FrameStructInit <= USED 0
SAI_GetCmdStatus <= USED 0
SAI_GetFIFOStatus <= USED 0
SAI_GetFlagStatus <= USED 0
SAI_GetITStatus <= USED 0
SAI_ITConfig <= USED 0
SAI_Init <= USED 0
SAI_MonoModeConfig <= USED 0
SAI_MuteFrameCounterConfig <= USED 0
SAI_MuteModeCmd <= USED 0
SAI_MuteValueConfig <= USED 0
SAI_ReceiveData <= USED 0
SAI_SendData <= USED 0
SAI_SlotInit <= USED 0
SAI_SlotStructInit <= USED 0
SAI_StructInit <= USED 0
SAI_TRIStateConfig <= USED 0
__asm___15_stm32f4xx_sai_c_191626f0____REV16 <= USED 0
__asm___15_stm32f4xx_sai_c_191626f0____REVSH <= USED 0
;FILE stm32f4xx_sdio.o
SDIO_CEATAITCmd <= USED 0
SDIO_ClearFlag <= USED 0
SDIO_ClearITPendingBit <= USED 0
SDIO_ClockCmd <= USED 0
SDIO_CmdStructInit <= USED 0
SDIO_CommandCompletionCmd <= USED 0
SDIO_DMACmd <= USED 0
SDIO_DataConfig <= USED 0
SDIO_DataStructInit <= USED 0
SDIO_DeInit <= USED 0
SDIO_GetCommandResponse <= USED 0
SDIO_GetDataCounter <= USED 0
SDIO_GetFIFOCount <= USED 0
SDIO_GetFlagStatus <= USED 0
SDIO_GetITStatus <= USED 0
SDIO_GetPowerState <= USED 0
SDIO_GetResponse <= USED 0
SDIO_ITConfig <= USED 0
SDIO_Init <= USED 0
SDIO_ReadData <= USED 0
SDIO_SendCEATACmd <= USED 0
SDIO_SendCommand <= USED 0
SDIO_SendSDIOSuspendCmd <= USED 0
SDIO_SetPowerState <= USED 0
SDIO_SetSDIOOperation <= USED 0
SDIO_SetSDIOReadWaitMode <= USED 0
SDIO_StartSDIOReadWait <= USED 0
SDIO_StopSDIOReadWait <= USED 0
SDIO_StructInit <= USED 0
SDIO_WriteData <= USED 0
__asm___16_stm32f4xx_sdio_c_c8827541____REV16 <= USED 0
__asm___16_stm32f4xx_sdio_c_c8827541____REVSH <= USED 0
;FILE stm32f4xx_spi.o
I2S_Cmd <= USED 0
I2S_FullDuplexConfig <= USED 0
I2S_Init <= USED 0
I2S_StructInit <= USED 0
SPI_BiDirectionalLineConfig <= USED 0
SPI_CalculateCRC <= USED 0
SPI_Cmd <= USED 0
SPI_DataSizeConfig <= USED 0
SPI_GetCRC <= USED 0
SPI_GetCRCPolynomial <= USED 0
SPI_I2S_ClearFlag <= USED 0
SPI_I2S_ClearITPendingBit <= USED 0
SPI_I2S_DMACmd <= USED 0
SPI_I2S_DeInit <= USED 0
SPI_I2S_GetFlagStatus <= USED 0
SPI_I2S_GetITStatus <= USED 0
SPI_I2S_ITConfig <= USED 0
SPI_I2S_ReceiveData <= USED 0
SPI_I2S_SendData <= USED 0
SPI_Init <= USED 0
SPI_NSSInternalSoftwareConfig <= USED 0
SPI_SSOutputCmd <= USED 0
SPI_StructInit <= USED 0
SPI_TIModeCmd <= USED 0
SPI_TransmitCRC <= USED 0
__asm___15_stm32f4xx_spi_c_2b928927____REV16 <= USED 0
__asm___15_stm32f4xx_spi_c_2b928927____REVSH <= USED 0
;FILE stm32f4xx_syscfg.o
SYSCFG_CompensationCellCmd <= USED 0
SYSCFG_DeInit <= USED 0
SYSCFG_ETH_MediaInterfaceConfig <= USED 0
SYSCFG_EXTILineConfig <= USED 0
SYSCFG_GetCompensationCellStatus <= USED 0
SYSCFG_MemoryRemapConfig <= USED 0
SYSCFG_MemorySwappingBank <= USED 0
__asm___18_stm32f4xx_syscfg_c_57637610____REV16 <= USED 0
__asm___18_stm32f4xx_syscfg_c_57637610____REVSH <= USED 0
;FILE stm32f4xx_tim.o
TIM_BDTRConfig <= USED 0
TIM_BDTRStructInit <= USED 0
TIM_CCPreloadControl <= USED 0
TIM_CCxNCmd <= USED 0
TIM_ClearFlag <= USED 0
TIM_ClearOC1Ref <= USED 0
TIM_ClearOC2Ref <= USED 0
TIM_ClearOC3Ref <= USED 0
TIM_ClearOC4Ref <= USED 0
TIM_CounterModeConfig <= USED 0
TIM_DMACmd <= USED 0
TIM_DMAConfig <= USED 0
TIM_DeInit <= USED 0
TIM_ETRClockMode1Config <= USED 0
TIM_ETRClockMode2Config <= USED 0
TIM_ETRConfig <= USED 0
TIM_EncoderInterfaceConfig <= USED 0
TIM_ForcedOC1Config <= USED 0
TIM_ForcedOC2Config <= USED 0
TIM_ForcedOC3Config <= USED 0
TIM_ForcedOC4Config <= USED 0
TIM_GetCapture1 <= USED 0
TIM_GetCapture2 <= USED 0
TIM_GetCapture3 <= USED 0
TIM_GetCapture4 <= USED 0
TIM_GetCounter <= USED 0
TIM_GetFlagStatus <= USED 0
TIM_GetPrescaler <= USED 0
TIM_ICInit <= USED 0
TIM_ICStructInit <= USED 0
TIM_ITConfig <= USED 0
TIM_ITRxExternalClockConfig <= USED 0
TIM_InternalClockConfig <= USED 0
TIM_OC1FastConfig <= USED 0
TIM_OC1NPolarityConfig <= USED 0
TIM_OC1PolarityConfig <= USED 0
TIM_OC2FastConfig <= USED 0
TIM_OC2Init <= USED 0
TIM_OC2NPolarityConfig <= USED 0
TIM_OC2PolarityConfig <= USED 0
TIM_OC2PreloadConfig <= USED 0
TIM_OC3FastConfig <= USED 0
TIM_OC3NPolarityConfig <= USED 0
TIM_OC3PolarityConfig <= USED 0
TIM_OC4FastConfig <= USED 0
TIM_OC4Init <= USED 0
TIM_OC4PolarityConfig <= USED 0
TIM_OC4PreloadConfig <= USED 0
TIM_OCStructInit <= USED 0
TIM_PWMIConfig <= USED 0
TIM_PrescalerConfig <= USED 0
TIM_RemapConfig <= USED 0
TIM_SelectCCDMA <= USED 0
TIM_SelectCOM <= USED 0
TIM_SelectHallSensor <= USED 0
TIM_SelectInputTrigger <= USED 0
TIM_SelectMasterSlaveMode <= USED 0
TIM_SelectOCxM <= USED 0
TIM_SelectOnePulseMode <= USED 0
TIM_SelectOutputTrigger <= USED 0
TIM_SelectSlaveMode <= USED 0
TIM_SetAutoreload <= USED 0
TIM_SetClockDivision <= USED 0
TIM_SetCompare1 <= USED 0
TIM_SetCompare2 <= USED 0
TIM_SetCompare3 <= USED 0
TIM_SetCompare4 <= USED 0
TIM_SetCounter <= USED 0
TIM_SetIC1Prescaler <= USED 0
TIM_SetIC2Prescaler <= USED 0
TIM_SetIC3Prescaler <= USED 0
TIM_SetIC4Prescaler <= USED 0
TIM_TIxExternalClockConfig <= USED 0
TIM_TimeBaseStructInit <= USED 0
TIM_UpdateDisableConfig <= USED 0
TIM_UpdateRequestConfig <= USED 0
__asm___15_stm32f4xx_tim_c_c458916b____REV16 <= USED 0
__asm___15_stm32f4xx_tim_c_c458916b____REVSH <= USED 0
;FILE stm32f4xx_usart.o
USART_ClearFlag <= USED 0
USART_ClearITPendingBit <= USED 0
USART_ClockInit <= USED 0
USART_ClockStructInit <= USED 0
USART_DMACmd <= USED 0
USART_DeInit <= USED 0
USART_GetFlagStatus <= USED 0
USART_HalfDuplexCmd <= USED 0
USART_IrDACmd <= USED 0
USART_IrDAConfig <= USED 0
USART_LINBreakDetectLengthConfig <= USED 0
USART_LINCmd <= USED 0
USART_OneBitMethodCmd <= USED 0
USART_OverSampling8Cmd <= USED 0
USART_ReceiverWakeUpCmd <= USED 0
USART_SendBreak <= USED 0
USART_SendData <= USED 0
USART_SetAddress <= USED 0
USART_SetGuardTime <= USED 0
USART_SetPrescaler <= USED 0
USART_SmartCardCmd <= USED 0
USART_SmartCardNACKCmd <= USED 0
USART_StructInit <= USED 0
USART_WakeUpConfig <= USED 0
__asm___17_stm32f4xx_usart_c_9565154b____REV16 <= USED 0
__asm___17_stm32f4xx_usart_c_9565154b____REVSH <= USED 0
;FILE stm32f4xx_wwdg.o
WWDG_ClearFlag <= USED 0
WWDG_DeInit <= USED 0
WWDG_Enable <= USED 0
WWDG_EnableIT <= USED 0
WWDG_GetFlagStatus <= USED 0
WWDG_SetCounter <= USED 0
WWDG_SetPrescaler <= USED 0
WWDG_SetWindowValue <= USED 0
__asm___16_stm32f4xx_wwdg_c_9668c0ca____REV16 <= USED 0
__asm___16_stm32f4xx_wwdg_c_9668c0ca____REVSH <= USED 0
;FILE sys.o
INTX_DISABLE <= USED 0
INTX_ENABLE <= USED 0
MSR_MSP <= USED 0
WFI_SET <= USED 0
__asm___5_sys_c_WFI_SET____REV16 <= USED 0
__asm___5_sys_c_WFI_SET____REVSH <= USED 0
;FILE system_stm32f4xx.o
SystemCoreClockUpdate <= USED 0
__asm___18_system_stm32f4xx_c_5d646a67____REV16 <= USED 0
__asm___18_system_stm32f4xx_c_5d646a67____REVSH <= USED 0
;FILE tim.o
TIM2_Init <= USED 0
__asm___5_TIM_c_6e4aa341____REV16 <= USED 0
__asm___5_TIM_c_6e4aa341____REVSH <= USED 0
;FILE usart.o
__asm___7_usart_c_9275e5e5____REV16 <= USED 0
__asm___7_usart_c_9275e5e5____REVSH <= USED 0
