{"nncase_version": "2.9.0", "chip_type": "k230", "inference_width": 640, "inference_height": 640, "confidence_threshold": 0.5, "nms_threshold": 0.5, "calibrate_method": "NoClip", "ptq_option": "w:uint8 d:uint8", "export_kmodel_name": "best_AnchorBaseDet_can2_5_n_20250731090100.npy", "model_type": "AnchorBaseDet", "img_size": [640, 640], "anchors": [[132, 108, 111, 134, 155, 177], [221, 263, 386, 166, 211, 325], [273, 307, 361, 279, 379, 390]], "mean": [0.485, 0.456, 0.406], "std": [0.229, 0.224, 0.225], "categories": ["bazi"], "nms_option": false, "kmodel_path": "best_AnchorBaseDet_can2_5_n_20250731090100.kmodel", "num_classes": 1}