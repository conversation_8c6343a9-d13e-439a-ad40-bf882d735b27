/**
 ******************************************************************************
 * @file    main.c
 * <AUTHOR>
 * @version V4.0
 * @date    2025-08-02
 * @brief   STM32F407 K230通信瞄准控制系统
 *          
 *          本程序实现与K230视觉模块的通信和PID控制
 *          支持6字节协议接收和双轴电机精确控制
 * 
 * @note    系统配置:
 *          - 主控: STM32F407ZGT6 @ 168MHz
 *          - 通信: UART1 115200bps 与K230连接
 *          - 驱动: D36A双路步进电机驱动器 (1/16细分)
 *          
 *          控制逻辑:
 *          - 接收K230发送的6字节控制协议
 *          - PID算法处理像素偏差
 *          - 水平轴电机速度自适应控制
 ******************************************************************************
 * @attention
 * 本程序用于2025年全国大学生电子设计竞赛E题：简易自行瞄准装置
 * K230视觉通信版本 - 支持实时瞄准控制
 ******************************************************************************
 */

#include "sys.h"
#include "../HAREWARE/K230_COMM/k230_comm.h"
#include "../HAREWARE/KEY/KEY.h"
#include "../HAREWARE/LED/led.h"

// 全局系统时间计数器 (毫秒)
volatile uint32_t system_tick_ms = 0;

// 瞄准系统状态定义
typedef enum {
	AIM_STATE_IDLE = 0,     // 空闲状态，等待按键
	AIM_STATE_WORKING,      // 工作状态，正在瞄准
	AIM_STATE_COMPLETED     // 完成状态，瞄准成功
} AimState_t;

// 全局状态变量
static AimState_t aim_state = AIM_STATE_IDLE;
static volatile bool aim_completed_flag = false;  // 瞄准完成标志

/**
 * @brief  瞄准完成回调函数（由K230_COMM模块调用）
 * @param  None
 * @retval None
 * @note   当K230检测到瞄准完成时，会调用此函数设置完成标志
 */
void Set_Aim_Completed_Flag(void)
{
	aim_completed_flag = true;
}

/**
 * @brief  主函数 - K230通信瞄准控制系统
 * @param  None
 * @retval None
 * @note   程序入口点，实现K230通信和电机控制集成
 */
int main(void)
{
	// 测试计数器声明（必须在所有可执行语句之前）
	uint32_t test_counter = 0;
	
	/* ===== 系统基础初始化 ===== */
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); // 设置中断分组为2
	delay_init(168);                                 // 延时初始化(F407为168MHz)
	uart_init(115200);                               // K230通信串口初始化
	
	/* ===== 按键和LED初始化 ===== */
	Key_Init();                                      // K0按键初始化(PE4)
	LED_Init();                                      // LED初始化
	
	/* ===== 电机控制系统初始化 ===== */
	if(!Motor_System_Init()) {
		// 初始化失败则进入死循环
		while(1) {
			delay_ms(1000);
		}
	}
	
	/* ===== K230通信模块初始化 ===== */
	if(!K230_Comm_Init()) {
		// 初始化失败则进入死循环
		while(1) {
			delay_ms(1000);
		}
	}
	
	/* ===== 系统就绪，开始运行 ===== */
	// 使能水平轴电机
	Motor_Enable(MOTOR_AXIS_HORIZONTAL);
	
	// 设置默认速度
	Motor_SetSpeed(MOTOR_AXIS_HORIZONTAL, MOTOR_HORIZONTAL_SPEED_MEDIUM);
	
	delay_ms(1000); // 系统稳定延时
	
	/* ===== 串口测试：自动发送测试信息 ===== */
	printf("=== STM32F407 UART Test ===\n");
	delay_ms(500);
	printf("System Init OK\n");
	delay_ms(500);
	printf("printf redirect OK\n");
	delay_ms(500);
	printf("Baudrate: 115200\n");
	delay_ms(500);
	printf("UART1: PA9(TX) PA10(RX)\n");
	delay_ms(500);
	printf("Start loop test...\n");
	delay_ms(1000);
	
	/* ===== 主循环：K230通信测试 ===== */
	while(1)
	{
		// 每2秒循环发送START和STOP指令
		if(system_tick_ms % 4000 == 0) {
			test_counter++;
			printf("=== Test cycle %lu ===\n", test_counter);
			
			// 发送START指令
			printf("START");  // 发送给K230
			printf("Sent START to K230\n");  // 调试信息
			LED0_Set(1);  // 点亮LED表示发送START
			delay_ms(2000);  // 等待2秒
			
			// 发送STOP指令
			printf("STOP");   // 发送给K230
			printf("Sent STOP to K230\n");  // 调试信息
			LED0_Set(0);  // 熄灭LED表示发送STOP
			delay_ms(100);   // 短暂延时防止重复
		}
		
		// 简化按键测试：按键手动发送START指令
		if(Key_Scan_Debounce()) {
			printf("Manual START command\n");
			printf("START");  // 手动发送START给K230
			LED0_Toggle();  // 切换LED状态
		}
		
		// 短暂延时，避免过度占用CPU
		delay_ms(10);
	}
}
