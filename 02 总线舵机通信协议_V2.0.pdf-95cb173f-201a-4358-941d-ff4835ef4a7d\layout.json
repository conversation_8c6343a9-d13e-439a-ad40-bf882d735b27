{"pdf_info": [{"para_blocks": [{"bbox": [163, 94, 429, 118], "type": "title", "lines": [{"bbox": [163, 94, 429, 118], "spans": [{"bbox": [163, 94, 429, 118], "type": "text", "content": "幻尔科技总线舵机通信协议"}]}], "index": 0, "level": 1}, {"bbox": [89, 165, 129, 182], "type": "title", "lines": [{"bbox": [89, 165, 129, 182], "spans": [{"bbox": [89, 165, 129, 182], "type": "text", "content": "1.概要"}]}], "index": 1, "level": 1}, {"bbox": [88, 192, 506, 330], "type": "text", "lines": [{"bbox": [88, 192, 506, 330], "spans": [{"bbox": [88, 192, 506, 330], "type": "text", "content": "采用异步串行总线通讯方式，理论多至253个机器人舵机可以通过总线组成链型，通过UART异步串行接口统一控制。每个舵机可以设定不同的节点地址，多个舵机可以统一运动也可以单个独立控制。通过异步串行接口与用户的上位机(控制器或PC机)通讯，您可对其进行参数设置、功能控制。通过异步串行接口发送指令，可以设置为电机控制模式或位置控制模式。在电机控制模式下，可以作为直流减速电机使用，速度可调；在位置控制模式下，拥有 "}, {"bbox": [88, 192, 506, 330], "type": "inline_equation", "content": "0 - 240^{\\circ}"}, {"bbox": [88, 192, 506, 330], "type": "text", "content": " 的转动范围，外加 "}, {"bbox": [88, 192, 506, 330], "type": "inline_equation", "content": "\\pm 30"}, {"bbox": [88, 192, 506, 330], "type": "text", "content": " 。的偏差可调范围，在此范围内具备精确位置控制性能，速度可调。只要符合协议的半双工UART异步串行接口都可以和舵机进行通讯，对舵机进行各种控制"}]}], "index": 2}, {"bbox": [89, 368, 207, 385], "type": "title", "lines": [{"bbox": [89, 368, 207, 385], "spans": [{"bbox": [89, 368, 207, 385], "type": "text", "content": "2.UART接口原理图"}]}], "index": 3, "level": 1}, {"bbox": [89, 394, 506, 439], "type": "text", "lines": [{"bbox": [89, 394, 506, 439], "spans": [{"bbox": [89, 394, 506, 439], "type": "text", "content": "舵机用程序代码对UART异步串行接口进行时序控制，实现半双工异步串行总线通讯，通讯波特率为115200bps，且接口简单、协议精简。在您自行设计的控制器中，用于和舵机通讯的UART接口必须如下图所示进行处理。"}]}], "index": 4}, {"bbox": [110, 440, 130, 452], "type": "text", "lines": [{"bbox": [110, 440, 130, 452], "spans": [{"bbox": [110, 440, 130, 452], "type": "text", "content": "图1"}]}], "index": 5}, {"type": "image", "bbox": [91, 464, 498, 706], "blocks": [{"bbox": [91, 464, 498, 706], "lines": [{"bbox": [91, 464, 498, 706], "spans": [{"bbox": [91, 464, 498, 706], "type": "image", "image_path": "8e416e311e119e9dd15c48899444c348c0332d79f0e4a68bf4772c29e6e84741.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 0}, {"para_blocks": [{"bbox": [89, 79, 144, 95], "type": "title", "lines": [{"bbox": [89, 79, 144, 95], "spans": [{"bbox": [89, 79, 144, 95], "type": "text", "content": "3.指令包"}]}], "index": 0, "level": 1}, {"bbox": [89, 102, 151, 116], "type": "text", "lines": [{"bbox": [89, 102, 151, 116], "spans": [{"bbox": [89, 102, 151, 116], "type": "text", "content": "指令包格式"}]}], "index": 1}, {"type": "table", "bbox": [88, 132, 501, 169], "blocks": [{"bbox": [88, 132, 501, 169], "lines": [{"bbox": [88, 132, 501, 169], "spans": [{"bbox": [88, 132, 501, 169], "type": "table", "html": "<table><tr><td>帧头</td><td>ID 号</td><td>数据长度</td><td>指令</td><td>参数</td><td>校验和</td></tr><tr><td>0x55 0x55</td><td>ID</td><td>Length</td><td>Cmd</td><td>Prm 1 ... Prm N</td><td>Checksum</td></tr></table>", "image_path": "3a9cf6f8a228c2bfebb5681a8a95663ad2d425ae0bd9207a66c29fb77d4f840c.jpg"}]}], "index": 3, "type": "table_body"}, {"bbox": [88, 121, 112, 132], "lines": [{"bbox": [88, 121, 112, 132], "spans": [{"bbox": [88, 121, 112, 132], "type": "text", "content": "表1："}]}], "index": 2, "type": "table_caption"}], "index": 3}, {"bbox": [89, 184, 333, 198], "type": "text", "lines": [{"bbox": [89, 184, 333, 198], "spans": [{"bbox": [89, 184, 333, 198], "type": "text", "content": "帧头：连续收到两个 "}, {"bbox": [89, 184, 333, 198], "type": "inline_equation", "content": "0\\mathrm{x}55"}, {"bbox": [89, 184, 333, 198], "type": "text", "content": " ，表示有数据包到达。"}]}], "index": 4}, {"bbox": [88, 214, 506, 276], "type": "text", "lines": [{"bbox": [88, 214, 506, 276], "spans": [{"bbox": [88, 214, 506, 276], "type": "text", "content": "ID：每个舵机都有一个ID号。ID号范围 "}, {"bbox": [88, 214, 506, 276], "type": "inline_equation", "content": "0^{\\sim}253"}, {"bbox": [88, 214, 506, 276], "type": "text", "content": " ，转换为十六进制 "}, {"bbox": [88, 214, 506, 276], "type": "inline_equation", "content": "0\\mathrm{x}00^{\\sim}0\\mathrm{xFD}"}, {"bbox": [88, 214, 506, 276], "type": "text", "content": " 广播ID：ID号254(0xFE)为广播ID,若控制器发出的ID号为254(0xFE)，所有的舵机均接收指令，但都不返回应答信息，（读取舵机ID号除外，具体说明参见下面指令介绍）以防总线冲突。"}]}], "index": 5}, {"bbox": [89, 292, 506, 324], "type": "text", "lines": [{"bbox": [89, 292, 506, 324], "spans": [{"bbox": [89, 292, 506, 324], "type": "text", "content": "数据长度：等于待发送的数据（包含本身一个字节）长度，即数据长度Length加3等于这一包指令的长度，从帧头到校验和。"}]}], "index": 6}, {"bbox": [88, 338, 359, 353], "type": "text", "lines": [{"bbox": [88, 338, 359, 353], "spans": [{"bbox": [88, 338, 359, 353], "type": "text", "content": "指令：控制舵机的各种指令，如位置、速度控制等。"}]}], "index": 7}, {"bbox": [89, 370, 287, 385], "type": "text", "lines": [{"bbox": [89, 370, 287, 385], "spans": [{"bbox": [89, 370, 287, 385], "type": "text", "content": "参数：除指令外需要补充的控制信息。"}]}], "index": 8}, {"bbox": [88, 401, 505, 446], "type": "text", "lines": [{"bbox": [88, 401, 505, 446], "spans": [{"bbox": [88, 401, 505, 446], "type": "text", "content": "校验和：校验和Checksum，计算方法如下：Checksum "}, {"bbox": [88, 401, 505, 446], "type": "inline_equation", "content": "= \\sim (\\mathrm{ID} + \\mathrm{Length} + \\mathrm{Cmd} + \\mathrm{Prm}1 + \\ldots \\mathrm{PrmN})"}, {"bbox": [88, 401, 505, 446], "type": "text", "content": " 若括号内的计算和超出255，则取最低的一个字节，“\\~”表示取反。"}]}], "index": 9}, {"bbox": [90, 464, 298, 478], "type": "text", "lines": [{"bbox": [90, 464, 298, 478], "spans": [{"bbox": [90, 464, 298, 478], "type": "text", "content": "（校验码计算方法可查看4. 校验码计算）"}]}], "index": 10}, {"bbox": [89, 518, 157, 535], "type": "title", "lines": [{"bbox": [89, 518, 157, 535], "spans": [{"bbox": [89, 518, 157, 535], "type": "text", "content": "3.指令类型"}]}], "index": 11, "level": 1}, {"bbox": [89, 544, 506, 605], "type": "text", "lines": [{"bbox": [89, 544, 506, 605], "spans": [{"bbox": [89, 544, 506, 605], "type": "text", "content": "3. 指令类型指令有两种，写指令和读指令。写指令：后面一般带有参数，将相应功能的参数写进舵机，来完成某种动作。读指令：后面一般不带参数，舵机接收到读指令后会立即返回相应数据，返回的指令值和发送给舵机的“读指令”值相同，并且带有参数。所以上位机发送读指令后要立马准备将自己变为读取状态。"}]}], "index": 12}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 1}, {"para_blocks": [{"bbox": [90, 87, 262, 101], "type": "text", "lines": [{"bbox": [90, 87, 262, 101], "spans": [{"bbox": [90, 87, 262, 101], "type": "text", "content": "下表是上位机发送个舵机的指令："}]}], "index": 0}, {"type": "table", "bbox": [88, 112, 384, 592], "blocks": [{"bbox": [88, 112, 384, 592], "lines": [{"bbox": [88, 112, 384, 592], "spans": [{"bbox": [88, 112, 384, 592], "type": "table", "html": "<table><tr><td>指令名</td><td>指令值</td><td>数据长度</td></tr><tr><td>SERVO_MOVE_TIME_WRITE</td><td>1</td><td>7</td></tr><tr><td>SERVO_MOVE_TIME_READ</td><td>2</td><td>3</td></tr><tr><td>SERVO_MOVE_TIME_WAIT_WRITE</td><td>7</td><td>7</td></tr><tr><td>SERVO_MOVE_TIME_WAIT_READ</td><td>8</td><td>3</td></tr><tr><td>SERVO_MOVE_START</td><td>11</td><td>3</td></tr><tr><td>SERVO_MOVE_STOP</td><td>12</td><td>3</td></tr><tr><td>SERVO_ID_WRITE</td><td>13</td><td>4</td></tr><tr><td>SERVO_ID_READ</td><td>14</td><td>3</td></tr><tr><td>SERVO_ANGLE_OFFSET_ADJUST</td><td>17</td><td>4</td></tr><tr><td>SERVO_ANGLE_OFFSET_WRITE</td><td>18</td><td>3</td></tr><tr><td>SERVO_ANGLE_OFFSET_READ</td><td>19</td><td>3</td></tr><tr><td>SERVO_ANGLE_LIMIT_WRITE</td><td>20</td><td>7</td></tr><tr><td>SERVO_ANGLE_LIMIT_READ</td><td>21</td><td>3</td></tr><tr><td>SERVO_VIN_LIMIT_WRITE</td><td>22</td><td>7</td></tr><tr><td>SERVO_VIN_LIMIT_READ</td><td>23</td><td>3</td></tr><tr><td>SERVO_TEMP_MAX_LIMIT_WRITE</td><td>24</td><td>4</td></tr><tr><td>SERVO_TEMP_MAX_LIMIT_READ</td><td>25</td><td>3</td></tr><tr><td>SERVO_TEMP_READ</td><td>26</td><td>3</td></tr><tr><td>SERVO_VIN_READ</td><td>27</td><td>3</td></tr><tr><td>SERVO_POS_READ</td><td>28</td><td>3</td></tr><tr><td>SERVO_OR_MOTOR_MODE_WRITE</td><td>29</td><td>7</td></tr><tr><td>SERVO_OR_MOTOR_MODE_READ</td><td>30</td><td>3</td></tr><tr><td>SERVO_LOAD_OR_UNLOAD_WRITE</td><td>31</td><td>4</td></tr><tr><td>SERVO_LOAD_OR_UNLOAD_READ</td><td>32</td><td>3</td></tr><tr><td>SERVO_LED_CTRL_WRITE</td><td>33</td><td>4</td></tr><tr><td>SERVO_LED_CTRL_READ</td><td>34</td><td>3</td></tr><tr><td>SERVO_LED_ERROR_WRITE</td><td>35</td><td>4</td></tr><tr><td>SERVO_LED_ERROR_READ</td><td>36</td><td>3</td></tr></table>", "image_path": "cc567b973733a2179ec75dc8b2347ea0b467255b987f17d051dac8ce1f08bf3d.jpg"}]}], "index": 2, "type": "table_body"}, {"bbox": [88, 105, 110, 116], "lines": [{"bbox": [88, 105, 110, 116], "spans": [{"bbox": [88, 105, 110, 116], "type": "text", "content": "表2"}]}], "index": 1, "type": "table_caption"}], "index": 2}, {"bbox": [88, 608, 498, 639], "type": "text", "lines": [{"bbox": [88, 608, 498, 639], "spans": [{"bbox": [88, 608, 498, 639], "type": "text", "content": "指令名：只是为了方便识别，用户也可以根据自己的习惯自行设定。指令名后缀为“_WRITE”代表写指令，后缀为“_READ”代表读指令。"}]}], "index": 3}, {"bbox": [89, 643, 498, 672], "type": "text", "lines": [{"bbox": [89, 643, 498, 672], "spans": [{"bbox": [89, 643, 498, 672], "type": "text", "content": "注意：在发送舵机控制指令时，需要等待前一条指令运行结束后，再发送下一条指令，否则旧指令的运行会被打断。"}]}], "index": 4}, {"bbox": [89, 687, 287, 702], "type": "text", "lines": [{"bbox": [89, 687, 287, 702], "spans": [{"bbox": [89, 687, 287, 702], "type": "text", "content": "指令值：即表1中指令包的指令Cmd。"}]}], "index": 5}, {"bbox": [89, 718, 285, 733], "type": "text", "lines": [{"bbox": [89, 718, 285, 733], "spans": [{"bbox": [89, 718, 285, 733], "type": "text", "content": "数据长度：即表1中数据长度Length"}]}], "index": 6}, {"bbox": [88, 750, 504, 797], "type": "text", "lines": [{"bbox": [88, 750, 504, 797], "spans": [{"bbox": [88, 750, 504, 797], "type": "text", "content": "1．指令名SERVO_MOVE_TIME_WRITE指令值1数据长度7；参数1：角度的低八位。参数2：角度的高八位。范围0~1000，对应舵机角度的0~240°，即舵机可变化"}]}], "index": 7}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 2}, {"para_blocks": [{"bbox": [89, 72, 206, 85], "type": "text", "lines": [{"bbox": [89, 72, 206, 85], "spans": [{"bbox": [89, 72, 206, 85], "type": "text", "content": "的最小角度为0.24度。"}]}], "index": 0}, {"bbox": [89, 88, 200, 102], "type": "text", "lines": [{"bbox": [89, 88, 200, 102], "spans": [{"bbox": [89, 88, 200, 102], "type": "text", "content": "参数3：时间低八位。"}]}], "index": 1}, {"bbox": [89, 105, 506, 150], "type": "text", "lines": [{"bbox": [89, 105, 506, 150], "spans": [{"bbox": [89, 105, 506, 150], "type": "text", "content": "参数4：时间高八位，时间的范围 "}, {"bbox": [89, 105, 506, 150], "type": "inline_equation", "content": "0^{\\sim}30000"}, {"bbox": [89, 105, 506, 150], "type": "text", "content": " 毫秒。该命令发送给舵机，舵机将在参数时间内从当前角度匀速转动到参数角度。该指令到达舵机后，舵机会立即转动。"}]}], "index": 2}, {"bbox": [88, 163, 114, 175], "type": "text", "lines": [{"bbox": [88, 163, 114, 175], "spans": [{"bbox": [88, 163, 114, 175], "type": "text", "content": "举例："}]}], "index": 3}, {"bbox": [112, 177, 380, 230], "type": "text", "lines": [{"bbox": [112, 177, 380, 230], "spans": [{"bbox": [112, 177, 380, 230], "type": "text", "content": "1）使总线舵机角度转到500，时间为1000ms，命令如下：5555010701F401E803crc2）使总线舵机角度转到1000，时间为300ms，命令如下：5555010701E8032C01crc"}]}], "index": 4}, {"bbox": [88, 247, 393, 260], "type": "text", "lines": [{"bbox": [88, 247, 393, 260], "spans": [{"bbox": [88, 247, 393, 260], "type": "text", "content": "2．指令名SERVO_MOVE_TIME_READ指令值2数据长度3："}]}], "index": 5}, {"bbox": [89, 264, 505, 293], "type": "text", "lines": [{"bbox": [89, 264, 505, 293], "spans": [{"bbox": [89, 264, 505, 293], "type": "text", "content": "读取指令名SERVO_MOVE_TIME_WRITE发送给舵机的角度和时间值，舵机返回给上位机的指令包详情请参见下面表4的说明。"}]}], "index": 6}, {"bbox": [88, 327, 418, 342], "type": "text", "lines": [{"bbox": [88, 327, 418, 342], "spans": [{"bbox": [88, 327, 418, 342], "type": "text", "content": "3. 指令名SERVO_MOVE_TIME_WAIT_WRITE指令值7数据长度7："}]}], "index": 7}, {"bbox": [89, 343, 238, 358], "type": "text", "lines": [{"bbox": [89, 343, 238, 358], "spans": [{"bbox": [89, 343, 238, 358], "type": "text", "content": "参数1：预设角度的低八位。"}]}], "index": 8}, {"bbox": [89, 359, 502, 388], "type": "text", "lines": [{"bbox": [89, 359, 502, 388], "spans": [{"bbox": [89, 359, 502, 388], "type": "text", "content": "参数2：预设角度的高八位。范围 "}, {"bbox": [89, 359, 502, 388], "type": "inline_equation", "content": "0^{\\sim}1000"}, {"bbox": [89, 359, 502, 388], "type": "text", "content": " ，对应舵机角度的 "}, {"bbox": [89, 359, 502, 388], "type": "inline_equation", "content": "0^{\\sim}240^{\\circ}"}, {"bbox": [89, 359, 502, 388], "type": "text", "content": " ，即舵机可变化的最小角度为0.24度。"}]}], "index": 9}, {"bbox": [89, 390, 224, 404], "type": "text", "lines": [{"bbox": [89, 390, 224, 404], "spans": [{"bbox": [89, 390, 224, 404], "type": "text", "content": "参数3：预设时间低八位。"}]}], "index": 10}, {"bbox": [88, 407, 507, 467], "type": "text", "lines": [{"bbox": [88, 407, 507, 467], "spans": [{"bbox": [88, 407, 507, 467], "type": "text", "content": "参数4：预设时间高八位，时间的范围 "}, {"bbox": [88, 407, 507, 467], "type": "inline_equation", "content": "0^{\\sim}30000"}, {"bbox": [88, 407, 507, 467], "type": "text", "content": " 毫秒。该指令跟第1点中SERVO_MOVE_TIME_WRITE指令功能相似，不同的是该指令到达舵机后，舵机不会立即转动，需要等待指令名SERVO_MOVE_START指令值为11的指令送达舵机后，舵机才会转动，将在参数时间内从当前角度匀速转动到参数角度。"}]}], "index": 11}, {"bbox": [88, 480, 114, 492], "type": "text", "lines": [{"bbox": [88, 480, 114, 492], "spans": [{"bbox": [88, 480, 114, 492], "type": "text", "content": "举例："}]}], "index": 12}, {"bbox": [111, 493, 380, 547], "type": "text", "lines": [{"bbox": [111, 493, 380, 547], "spans": [{"bbox": [111, 493, 380, 547], "type": "text", "content": "1）使总线舵机角度转到500，时间为1000ms，命令如下：5555010707F401E803crc2）使总线舵机角度转到1000，时间为300ms，命令如下：5555010707E8032C01crc"}]}], "index": 13}, {"bbox": [88, 575, 424, 589], "type": "text", "lines": [{"bbox": [88, 575, 424, 589], "spans": [{"bbox": [88, 575, 424, 589], "type": "text", "content": "4．指令名SERVO_MOVE_TIME_WAIT_READ指令值8数据长度3："}]}], "index": 14}, {"bbox": [89, 592, 495, 622], "type": "text", "lines": [{"bbox": [89, 592, 495, 622], "spans": [{"bbox": [89, 592, 495, 622], "type": "text", "content": "读取指令名SERVO_MOVE_TIME_WAIT_WRITE发送给舵机的预设角度和预设时间值，舵机返回给上位机的指令包详情请参见下面表3的说明。"}]}], "index": 15}, {"bbox": [88, 655, 452, 686], "type": "text", "lines": [{"bbox": [88, 655, 452, 686], "spans": [{"bbox": [88, 655, 452, 686], "type": "text", "content": "5．指令名SERVO_MOVE_START指令值11数据长度3：配合指令SERVO_MOVE_TIME_WAIT_WRITE使用，在第3点中有说明。"}]}], "index": 16}, {"bbox": [88, 702, 114, 714], "type": "text", "lines": [{"bbox": [88, 702, 114, 714], "spans": [{"bbox": [88, 702, 114, 714], "type": "text", "content": "举例："}]}], "index": 17}, {"bbox": [113, 715, 287, 742], "type": "text", "lines": [{"bbox": [113, 715, 287, 742], "spans": [{"bbox": [113, 715, 287, 742], "type": "text", "content": "1）向总线舵机1发送运行命令，如下：555501030Bcrc"}]}], "index": 18}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 3}, {"para_blocks": [{"bbox": [88, 70, 502, 99], "type": "text", "lines": [{"bbox": [88, 70, 502, 99], "spans": [{"bbox": [88, 70, 502, 99], "type": "text", "content": "6．指令名 SERVO_MOVE_STOP 指令值 12 数据长度 3: 该指令到达舵机，如果舵机正在转动，就会立即停止转动。停在当前角度位置。"}]}], "index": 0}, {"bbox": [88, 114, 114, 126], "type": "text", "lines": [{"bbox": [88, 114, 114, 126], "spans": [{"bbox": [88, 114, 114, 126], "type": "text", "content": "举例："}]}], "index": 1}, {"bbox": [113, 130, 287, 157], "type": "text", "lines": [{"bbox": [113, 130, 287, 157], "spans": [{"bbox": [113, 130, 287, 157], "type": "text", "content": "1）向总线舵机1发送停止命令，如下：555501030Ccrc"}]}], "index": 2}, {"bbox": [88, 162, 500, 208], "type": "text", "lines": [{"bbox": [88, 162, 500, 208], "spans": [{"bbox": [88, 162, 500, 208], "type": "text", "content": "7．指令名 SERVO_ID_WRITE 指令值 13 数据长度 4: 参数 1：舵机的 ID 值，范围 0~253，默认为 1。该指令会重新给舵机写入 ID 值，并且掉电保存。"}]}], "index": 3}, {"bbox": [88, 222, 114, 235], "type": "text", "lines": [{"bbox": [88, 222, 114, 235], "spans": [{"bbox": [88, 222, 114, 235], "type": "text", "content": "举例："}]}], "index": 4}, {"bbox": [111, 238, 280, 299], "type": "text", "lines": [{"bbox": [111, 238, 280, 299], "spans": [{"bbox": [111, 238, 280, 299], "type": "text", "content": "1）将ID为1的舵机设置舵机ID号为255501040D02crc将ID为2的舵机设置舵机ID号为1055502040D0Acrc"}]}], "index": 5}, {"bbox": [88, 317, 484, 348], "type": "text", "lines": [{"bbox": [88, 317, 484, 348], "spans": [{"bbox": [88, 317, 484, 348], "type": "text", "content": "8．指令名 SERVO_ID_READ 指令值 14 数据长度 3: 读取舵机的 ID 值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 6}, {"bbox": [88, 391, 423, 405], "type": "text", "lines": [{"bbox": [88, 391, 423, 405], "spans": [{"bbox": [88, 391, 423, 405], "type": "text", "content": "9．指令名 SERVO_ANGLE_OFFSET_ADJUST 指令值 17 数据长度 4:"}]}], "index": 7}, {"bbox": [88, 408, 506, 436], "type": "text", "lines": [{"bbox": [88, 408, 506, 436], "spans": [{"bbox": [88, 408, 506, 436], "type": "text", "content": "参数 1：舵机内部的偏差值，范围 "}, {"bbox": [88, 408, 506, 436], "type": "inline_equation", "content": "- 125^{\\circ} - 125"}, {"bbox": [88, 408, 506, 436], "type": "text", "content": " ，对应角度为 "}, {"bbox": [88, 408, 506, 436], "type": "inline_equation", "content": "- 30^{\\circ} - 30^{\\circ}"}, {"bbox": [88, 408, 506, 436], "type": "text", "content": " ，该指令到达舵机，舵机将立即转动进行偏差调整。"}]}], "index": 8}, {"bbox": [88, 439, 507, 498], "type": "text", "lines": [{"bbox": [88, 439, 507, 498], "spans": [{"bbox": [88, 439, 507, 498], "type": "text", "content": "注意 1：通过此指令调整好的偏差值不支持掉电保存，想要保存请参考第 10 点。注意 2：因为该参数为 signed char 型数据，而发送的指令包都为 unsigned char 型数据，所以发送之前要做强制转换成 unsigned char 型数据放到指令包中进行发送"}]}], "index": 9}, {"bbox": [88, 523, 114, 534], "type": "text", "lines": [{"bbox": [88, 523, 114, 534], "spans": [{"bbox": [88, 523, 114, 534], "type": "text", "content": "举例："}]}], "index": 10}, {"bbox": [113, 536, 252, 562], "type": "text", "lines": [{"bbox": [113, 536, 252, 562], "spans": [{"bbox": [113, 536, 252, 562], "type": "text", "content": "1）设置总线舵机1的偏差为6：555501041106crc"}]}], "index": 11}, {"bbox": [88, 602, 439, 633], "type": "text", "lines": [{"bbox": [88, 602, 439, 633], "spans": [{"bbox": [88, 602, 439, 633], "type": "text", "content": "10．指令名 SERVO_ANGLE_OFFSET_WRITE 指令值 18 数据长度 3: 保存偏差值，并支持掉电保存。其偏差值的调整已在第 9 点中说明。"}]}], "index": 12}, {"bbox": [88, 658, 114, 671], "type": "text", "lines": [{"bbox": [88, 658, 114, 671], "spans": [{"bbox": [88, 658, 114, 671], "type": "text", "content": "举例："}]}], "index": 13}, {"bbox": [113, 671, 252, 685], "type": "text", "lines": [{"bbox": [113, 671, 252, 685], "spans": [{"bbox": [113, 671, 252, 685], "type": "text", "content": "1）设置总线舵机1的偏差为6："}]}], "index": 14}, {"bbox": [112, 698, 202, 712], "type": "text", "lines": [{"bbox": [112, 698, 202, 712], "spans": [{"bbox": [112, 698, 202, 712], "type": "text", "content": "5555010312crc"}]}], "index": 15}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 4}, {"para_blocks": [{"bbox": [88, 70, 499, 100], "type": "text", "lines": [{"bbox": [88, 70, 499, 100], "spans": [{"bbox": [88, 70, 499, 100], "type": "text", "content": "11. 指令名 SERVO_ANGLE_OFFSET_READ 指令值 19 数据长度 3: 读取舵机设定的偏差值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 0}, {"bbox": [88, 116, 414, 131], "type": "text", "lines": [{"bbox": [88, 116, 414, 131], "spans": [{"bbox": [88, 116, 414, 131], "type": "text", "content": "12. 指令名 SERVO_ANGLE_LIMIT_WRITE 指令值 20 数据长度 7:"}]}], "index": 1}, {"bbox": [89, 133, 311, 178], "type": "text", "lines": [{"bbox": [89, 133, 311, 178], "spans": [{"bbox": [89, 133, 311, 178], "type": "text", "content": "参数 1：最小角度的低八位。参数 2：最小角度的高八位。范围 "}, {"bbox": [89, 133, 311, 178], "type": "inline_equation", "content": "0^{\\sim}1000"}, {"bbox": [89, 133, 311, 178], "type": "text", "content": " 。参数 3：最大角度的低八位。"}]}], "index": 2}, {"bbox": [89, 181, 506, 225], "type": "text", "lines": [{"bbox": [89, 181, 506, 225], "spans": [{"bbox": [89, 181, 506, 225], "type": "text", "content": "参数 4：最大角度的高八位。范围 "}, {"bbox": [89, 181, 506, 225], "type": "inline_equation", "content": "0^{\\sim}1000"}, {"bbox": [89, 181, 506, 225], "type": "text", "content": " 。且最小角度值要始终小于最大角度值。该命令发送给舵机，舵机的转动角度将被限制在最小与最大之间转动。并且角度限制值支持掉电保存。"}]}], "index": 3}, {"bbox": [88, 238, 114, 250], "type": "text", "lines": [{"bbox": [88, 238, 114, 250], "spans": [{"bbox": [88, 238, 114, 250], "type": "text", "content": "举例："}]}], "index": 4}, {"bbox": [111, 253, 396, 280], "type": "text", "lines": [{"bbox": [111, 253, 396, 280], "spans": [{"bbox": [111, 253, 396, 280], "type": "text", "content": "1）设置总线舵机1的最小和最大角度分别为200、800，如下：5555010714C8002003crc"}]}], "index": 5}, {"bbox": [88, 296, 500, 326], "type": "text", "lines": [{"bbox": [88, 296, 500, 326], "spans": [{"bbox": [88, 296, 500, 326], "type": "text", "content": "13. 指令名 SERVO_ANGLE_LIMIT_READ 指令值 21 数据长度 3: 读取舵机角度的限制值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 6}, {"bbox": [88, 342, 396, 356], "type": "text", "lines": [{"bbox": [88, 342, 396, 356], "spans": [{"bbox": [88, 342, 396, 356], "type": "text", "content": "14. 指令名 SERVO_VIN_LIMIT_WRITE 指令值 22 数据长度 7:"}]}], "index": 7}, {"bbox": [89, 359, 387, 404], "type": "text", "lines": [{"bbox": [89, 359, 387, 404], "spans": [{"bbox": [89, 359, 387, 404], "type": "text", "content": "参数 1：最小输入电压的低八位。参数 2：最小输入电压的高八位。范围 "}, {"bbox": [89, 359, 387, 404], "type": "inline_equation", "content": "4500^{\\sim}14000"}, {"bbox": [89, 359, 387, 404], "type": "text", "content": " 毫伏。参数 3：最大输入电压的低八位。"}]}], "index": 8}, {"bbox": [89, 407, 506, 482], "type": "text", "lines": [{"bbox": [89, 407, 506, 482], "spans": [{"bbox": [89, 407, 506, 482], "type": "text", "content": "参数 4：最大输入电压的高八位。范围 "}, {"bbox": [89, 407, 506, 482], "type": "inline_equation", "content": "4500^{\\sim}14000"}, {"bbox": [89, 407, 506, 482], "type": "text", "content": " 毫伏。且最小输入电压值要始终小于最大输入电压值。该命令发送给舵机，舵机的输入电压将被限制在最小与最大之间。超出范围舵机的 LED 灯将会闪烁报警，（如果设置了 LED 报警）为了保护舵机，其内的电机将会处于卸载断电状态，此时舵机将不会输出力矩，并且输入电压限制值支持掉电保存。"}]}], "index": 9}, {"bbox": [88, 496, 114, 507], "type": "text", "lines": [{"bbox": [88, 496, 114, 507], "spans": [{"bbox": [88, 496, 114, 507], "type": "text", "content": "举例："}]}], "index": 10}, {"bbox": [111, 509, 429, 535], "type": "text", "lines": [{"bbox": [111, 509, 429, 535], "spans": [{"bbox": [111, 509, 429, 535], "type": "text", "content": "1）设置总线舵机输入电压最小和最大值分别为5000、10000。如下：555501071688131027crc"}]}], "index": 11}, {"bbox": [88, 564, 506, 609], "type": "text", "lines": [{"bbox": [88, 564, 506, 609], "spans": [{"bbox": [88, 564, 506, 609], "type": "text", "content": "15. 指令名 SERVO_VIN_LIMIT_READ 指令值 23 数据长度 3: 读取舵机输入电压的限制值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 12}, {"bbox": [89, 626, 438, 640], "type": "text", "lines": [{"bbox": [89, 626, 438, 640], "spans": [{"bbox": [89, 626, 438, 640], "type": "text", "content": "16. 指令名 SERVO_TEMP_MAX_LIMIT_WRITE 指令值 24 数据长度 4:"}]}], "index": 13}, {"bbox": [89, 644, 506, 704], "type": "text", "lines": [{"bbox": [89, 644, 506, 704], "spans": [{"bbox": [89, 644, 506, 704], "type": "text", "content": "参数 1：舵机内部最高温度限制，范围 "}, {"bbox": [89, 644, 506, 704], "type": "inline_equation", "content": "50^{\\sim}100^{\\circ}C"}, {"bbox": [89, 644, 506, 704], "type": "text", "content": " ，默认值 "}, {"bbox": [89, 644, 506, 704], "type": "inline_equation", "content": "85^{\\circ}C"}, {"bbox": [89, 644, 506, 704], "type": "text", "content": " ，如果舵机内部温度超过了此值，舵机的 LED 灯将会闪烁报警，（如果设置了 LED 报警）为了保护舵机，其内的电机将会处于卸载断电状态，此时舵机将不会输出力矩，直到温度低于此值舵机会再次进入工作状态，并且此值支持掉电保存。"}]}], "index": 14}, {"bbox": [88, 717, 114, 729], "type": "text", "lines": [{"bbox": [88, 717, 114, 729], "spans": [{"bbox": [88, 717, 114, 729], "type": "text", "content": "举例："}]}], "index": 15}, {"bbox": [111, 730, 310, 757], "type": "text", "lines": [{"bbox": [111, 730, 310, 757], "spans": [{"bbox": [111, 730, 310, 757], "type": "text", "content": "1）设置总线舵机1的最高温度为80，如下：555501041850crc"}]}], "index": 16}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 5}, {"para_blocks": [{"bbox": [88, 70, 493, 116], "type": "text", "lines": [{"bbox": [88, 70, 493, 116], "spans": [{"bbox": [88, 70, 493, 116], "type": "text", "content": "17. 指令名 SERVO_TEMP_MAX_LIMIT_READ 指令值 25 数据长度 3: 读取舵机内部最高温度限制的值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 0}, {"bbox": [88, 132, 498, 163], "type": "text", "lines": [{"bbox": [88, 132, 498, 163], "spans": [{"bbox": [88, 132, 498, 163], "type": "text", "content": "18. 指令名 SERVO_TEMP_READ 指令值 26 数据长度 3: 读取舵机内部实时温度，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 1}, {"bbox": [88, 190, 500, 235], "type": "text", "lines": [{"bbox": [88, 190, 500, 235], "spans": [{"bbox": [88, 190, 500, 235], "type": "text", "content": "19. 指令名 SERVO_VIN_READ 指令值 27 数据长度 3: 读取舵机内部当前的输入电压值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 2}, {"bbox": [88, 269, 500, 316], "type": "text", "lines": [{"bbox": [88, 269, 500, 316], "spans": [{"bbox": [88, 269, 500, 316], "type": "text", "content": "20. 指令名 SERVO_POS_READ 指令值 28 数据长度 3: 读取舵机当前的实际角度位置值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 3}, {"bbox": [88, 344, 442, 359], "type": "text", "lines": [{"bbox": [88, 344, 442, 359], "spans": [{"bbox": [88, 344, 442, 359], "type": "text", "content": "21. 指令名 SERVO_OR_MOTOR_MODE_WRITE 指令值 29 数据长度 7:"}]}], "index": 4}, {"bbox": [88, 361, 499, 390], "type": "text", "lines": [{"bbox": [88, 361, 499, 390], "spans": [{"bbox": [88, 361, 499, 390], "type": "text", "content": "参数 1：舵机模式，范围0或1，0代表位置控制模式，1代表电机控制模式，默认值0，"}]}], "index": 5}, {"bbox": [89, 391, 163, 404], "type": "text", "lines": [{"bbox": [89, 391, 163, 404], "spans": [{"bbox": [89, 391, 163, 404], "type": "text", "content": "参数2：空值，"}]}], "index": 6}, {"bbox": [89, 407, 249, 421], "type": "text", "lines": [{"bbox": [89, 407, 249, 421], "spans": [{"bbox": [89, 407, 249, 421], "type": "text", "content": "参数3：转动速度值的低八位。"}]}], "index": 7}, {"bbox": [89, 423, 506, 468], "type": "text", "lines": [{"bbox": [89, 423, 506, 468], "spans": [{"bbox": [89, 423, 506, 468], "type": "text", "content": "参数4：转动速度值的高八位。范围- 1000~1000，只在电机控制模式时有效，控制电机的转速，该值为负值代表反转，正值代表正转。写入的模式和速度不支持掉电保存。"}]}], "index": 8}, {"bbox": [109, 469, 140, 482], "type": "title", "lines": [{"bbox": [109, 469, 140, 482], "spans": [{"bbox": [109, 469, 140, 482], "type": "text", "content": "注意："}]}], "index": 9, "level": 1}, {"bbox": [88, 484, 498, 513], "type": "text", "lines": [{"bbox": [88, 484, 498, 513], "spans": [{"bbox": [88, 484, 498, 513], "type": "inline_equation", "content": "①"}, {"bbox": [88, 484, 498, 513], "type": "text", "content": " 由于转动速度为 signed short int 型数据，所以发送改指令包之前先将该数据强制转换成 unsigned short int 型数据在进行数据传输。"}]}], "index": 10}, {"bbox": [88, 516, 498, 576], "type": "text", "lines": [{"bbox": [88, 516, 498, 576], "spans": [{"bbox": [88, 516, 498, 576], "type": "inline_equation", "content": "②"}, {"bbox": [88, 516, 498, 576], "type": "text", "content": " 在指令中，参数3与参数4以16进制补码形式表示。计算方式：正数为16进制原码，负数为取二进制数按位取反再加1。例如：1000表示为：03E8，500表示为：01F4，- 1000表示为：FC18。具体计算方法可搜索补码相关知识进行了解。"}]}], "index": 11}, {"bbox": [88, 591, 114, 602], "type": "text", "lines": [{"bbox": [88, 591, 114, 602], "spans": [{"bbox": [88, 591, 114, 602], "type": "text", "content": "举例："}]}], "index": 12}, {"bbox": [111, 605, 389, 631], "type": "text", "lines": [{"bbox": [111, 605, 389, 631], "spans": [{"bbox": [111, 605, 389, 631], "type": "text", "content": "1）设置总线舵机1为电机控制模式，转动速度为100，如下：55.55.01.07.1D.01.00.64.00.crc"}]}], "index": 13}, {"bbox": [88, 647, 506, 693], "type": "text", "lines": [{"bbox": [88, 647, 506, 693], "spans": [{"bbox": [88, 647, 506, 693], "type": "text", "content": "22. 指令名 SERVO_OR_MOTOR_MODE_READ 指令值 30 数据长度 3: 读取舵机模式相关的参数，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 14}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 6}, {"para_blocks": [{"bbox": [88, 70, 444, 84], "type": "text", "lines": [{"bbox": [88, 70, 444, 84], "spans": [{"bbox": [88, 70, 444, 84], "type": "text", "content": "23. 指令名 SERVO_LOAD_OR_UNLOAD_WRITE 指令值 31 数据长度 4:"}]}], "index": 0}, {"bbox": [89, 88, 506, 117], "type": "text", "lines": [{"bbox": [89, 88, 506, 117], "spans": [{"bbox": [89, 88, 506, 117], "type": "text", "content": "参数 1：舵机内部电机是否卸载掉电，范围 0 或 1，0 代表卸载掉电，此时舵机无力矩输出。1 代表装载电机，此时舵机有力矩输出，默认值 0。"}]}], "index": 1}, {"bbox": [88, 137, 114, 149], "type": "text", "lines": [{"bbox": [88, 137, 114, 149], "spans": [{"bbox": [88, 137, 114, 149], "type": "text", "content": "举例："}]}], "index": 2}, {"bbox": [113, 151, 246, 178], "type": "text", "lines": [{"bbox": [113, 151, 246, 178], "spans": [{"bbox": [113, 151, 246, 178], "type": "text", "content": "1）使总线舵机 1 上电，如下：55 55 01 04 1F 01 crc"}]}], "index": 3}, {"bbox": [88, 205, 506, 250], "type": "text", "lines": [{"bbox": [88, 205, 506, 250], "spans": [{"bbox": [88, 205, 506, 250], "type": "text", "content": "24. 指令名 SERVO_LOAD_OR_UNLOAD_READ 指令值 32 数据长度 3：读取舵机内部电机的状态，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 4}, {"bbox": [88, 267, 498, 315], "type": "text", "lines": [{"bbox": [88, 267, 498, 315], "spans": [{"bbox": [88, 267, 498, 315], "type": "text", "content": "25. 指令名 SERVO_LED_CTRL_WRITE 指令值 33 数据长度 4：参数 1：LED 灯的亮灭状态，范围 0 或 1，0 代表 LED 常亮。1 代表 LED 常灭，默认 0，并且支持掉电保存。"}]}], "index": 5}, {"bbox": [88, 334, 114, 347], "type": "text", "lines": [{"bbox": [88, 334, 114, 347], "spans": [{"bbox": [88, 334, 114, 347], "type": "text", "content": "举例："}]}], "index": 6}, {"bbox": [113, 348, 277, 375], "type": "text", "lines": [{"bbox": [113, 348, 277, 375], "spans": [{"bbox": [113, 348, 277, 375], "type": "text", "content": "1）使总线舵机 1 的 LED 灯亮，如下：55 55 01 04 21 00 crc"}]}], "index": 7}, {"bbox": [88, 389, 492, 420], "type": "text", "lines": [{"bbox": [88, 389, 492, 420], "spans": [{"bbox": [88, 389, 492, 420], "type": "text", "content": "26. 指令名 SERVO_LED_CTRL_READ 指令值 34 数据长度 3：读取 LED 灯的状态，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 8}, {"bbox": [88, 449, 405, 464], "type": "text", "lines": [{"bbox": [88, 449, 405, 464], "spans": [{"bbox": [88, 449, 405, 464], "type": "text", "content": "27. 指令名 SERVO_LED_ERROR_WRITE 指令值 35 数据长度 4："}]}], "index": 9}, {"bbox": [88, 468, 511, 528], "type": "text", "lines": [{"bbox": [88, 468, 511, 528], "spans": [{"bbox": [88, 468, 511, 528], "type": "text", "content": "参数 1：舵机哪些故障会导致 LED 闪烁报警的值，范围 0~7。有三种故障会导致 LED 闪烁报警，不管 LED 灯的状态是常灭还是常亮。第一种是舵机内部温度超过最大温度限制值，（此值设定在第 16 点）。第二种是舵机输入电压超过限制值，（此值设定在第 14 点）。第三种是舵机发生堵转。该值对应故障报警关系如下表："}]}], "index": 10}, {"type": "table", "bbox": [88, 555, 232, 689], "blocks": [{"bbox": [88, 555, 232, 689], "lines": [{"bbox": [88, 555, 232, 689], "spans": [{"bbox": [88, 555, 232, 689], "type": "table", "html": "<table><tr><td>0</td><td>没有报警</td></tr><tr><td>1</td><td>过温</td></tr><tr><td>2</td><td>过压</td></tr><tr><td>3</td><td>过温和过压</td></tr><tr><td>4</td><td>堵转</td></tr><tr><td>5</td><td>过温和堵转</td></tr><tr><td>6</td><td>过压和堵转</td></tr><tr><td>7</td><td>过温、过压和堵转</td></tr></table>", "image_path": "3f27e70234293cddb8891441b22ff746f3302856c4492538f14503a0a433a278.jpg"}]}], "index": 12, "type": "table_body"}, {"bbox": [88, 544, 110, 555], "lines": [{"bbox": [88, 544, 110, 555], "spans": [{"bbox": [88, 544, 110, 555], "type": "text", "content": "表3"}]}], "index": 11, "type": "table_caption"}], "index": 12}, {"bbox": [88, 701, 114, 713], "type": "text", "lines": [{"bbox": [88, 701, 114, 713], "spans": [{"bbox": [88, 701, 114, 713], "type": "text", "content": "举例："}]}], "index": 13}, {"bbox": [113, 714, 308, 740], "type": "text", "lines": [{"bbox": [113, 714, 308, 740], "spans": [{"bbox": [113, 714, 308, 740], "type": "text", "content": "1）使总线舵机 1 的过温故障会报警，如下：55 55 01 04 23 01 crc"}]}], "index": 14}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 7}, {"para_blocks": [{"bbox": [88, 74, 502, 105], "type": "text", "lines": [{"bbox": [88, 74, 502, 105], "spans": [{"bbox": [88, 74, 502, 105], "type": "text", "content": "28. 指令名 SERVO_LED_ERROR_READ 指令值 36 数据长度 3；读取舵机故障报警值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。"}]}], "index": 0}, {"bbox": [88, 120, 114, 132], "type": "text", "lines": [{"bbox": [88, 120, 114, 132], "spans": [{"bbox": [88, 120, 114, 132], "type": "text", "content": "举例："}]}], "index": 1}, {"bbox": [112, 136, 242, 167], "type": "text", "lines": [{"bbox": [112, 136, 242, 167], "spans": [{"bbox": [112, 136, 242, 167], "type": "text", "content": "1）读取总线舵机1的故障值5555010324wc"}]}], "index": 2}, {"type": "table", "bbox": [88, 244, 385, 491], "blocks": [{"bbox": [88, 244, 385, 491], "lines": [{"bbox": [88, 244, 385, 491], "spans": [{"bbox": [88, 244, 385, 491], "type": "table", "html": "<table><tr><td>指令名</td><td>指令值</td><td>数据长度</td></tr><tr><td>SERVO_MOVE_TIME_READ</td><td>2</td><td>7</td></tr><tr><td>SERVO_MOVE_TIME_WAIT_READ</td><td>8</td><td>7</td></tr><tr><td>SERVO_ID_READ</td><td>14</td><td>4</td></tr><tr><td>SERVO_ANGLE_OFFSET_READ</td><td>19</td><td>4</td></tr><tr><td>SERVO_ANGLE_LIMIT_READ</td><td>21</td><td>7</td></tr><tr><td>SERVO_VIN_LIMIT_READ</td><td>23</td><td>7</td></tr><tr><td>SERVO_TEMP_MAX_LIMIT_READ</td><td>25</td><td>4</td></tr><tr><td>SERVO_TEMP_READ</td><td>26</td><td>4</td></tr><tr><td>SERVO_VIN_READ</td><td>27</td><td>5</td></tr><tr><td>SERVO_POS_READ</td><td>28</td><td>5</td></tr><tr><td>SERVO_OR_MOTOR_MODE_READ</td><td>30</td><td>7</td></tr><tr><td>SERVO_LOAD_OR_UNLOAD_READ</td><td>32</td><td>4</td></tr><tr><td>SERVO_LED_CTRL_READ</td><td>34</td><td>4</td></tr><tr><td>SERVO_LED_ERROR_READ</td><td>36</td><td>4</td></tr></table>", "image_path": "edf13a8cd1361e3064f2f0a01ebe2a7d272fd93454782906a4951d5f8c9edb5b.jpg"}]}], "index": 4, "type": "table_body"}, {"bbox": [88, 234, 109, 244], "lines": [{"bbox": [88, 234, 109, 244], "spans": [{"bbox": [88, 234, 109, 244], "type": "text", "content": "表4"}]}], "index": 3, "type": "table_caption"}], "index": 4}, {"bbox": [88, 555, 505, 616], "type": "text", "lines": [{"bbox": [88, 555, 505, 616], "spans": [{"bbox": [88, 555, 505, 616], "type": "text", "content": "表 4 是舵机返回给上位机的指令，这些指令只有在上位机给舵机发送了读取指令后舵机才会返回的指令，并且返回的指令值和上位机发给舵机的读取指令值是一致的，不同的是，此返回值都是带有参数的。返回的数据指令包与上位机发送个舵机的指令包格式是一样的，都如表 1 那样。"}]}], "index": 5}, {"bbox": [88, 629, 376, 708], "type": "text", "lines": [{"bbox": [88, 629, 376, 708], "spans": [{"bbox": [88, 629, 376, 708], "type": "text", "content": "1. 指令名 SERVO_MOVE_TIME_READ 指令值 2 数据长度 7；参数 1：角度的低八位。参数 2：角度的高八位。范围 0~1000。参数 3：时间低八位。参数 4：时间高八位，时间的范围 0~30000 毫秒。"}]}], "index": 6}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 8}, {"para_blocks": [{"bbox": [88, 73, 376, 148], "type": "text", "lines": [{"bbox": [88, 73, 376, 148], "spans": [{"bbox": [88, 73, 376, 148], "type": "text", "content": "2. 指令名 SERVO_MOVE_TIME_READ 指令值 8 数据长度 7: 参数 1: 预设角度的低八位。参数 2: 预设角度的高八位。范围 0~1000。参数 3: 预设时间低八位。参数 4: 预设时间高八位，时间的范围 0~30000 毫秒。"}]}], "index": 0}, {"bbox": [88, 167, 339, 180], "type": "text", "lines": [{"bbox": [88, 167, 339, 180], "spans": [{"bbox": [88, 167, 339, 180], "type": "text", "content": "3. 指令名 SERVO_ID_READ 指令值 14 数据长度 4:"}]}], "index": 1}, {"bbox": [89, 182, 249, 196], "type": "text", "lines": [{"bbox": [89, 182, 249, 196], "spans": [{"bbox": [89, 182, 249, 196], "type": "text", "content": "参数1：舵机ID值，默认值1。"}]}], "index": 2}, {"bbox": [88, 200, 506, 274], "type": "text", "lines": [{"bbox": [88, 200, 506, 274], "spans": [{"bbox": [88, 200, 506, 274], "type": "text", "content": "说明：ID 的读取比其他读指令多一个特殊的地方，如果该指令包的 ID 为广播 ID：254(0xFE)，舵机是会返回应答信息的，而其他的读指令在 ID 为广播 ID 时都是不返回应答信息的。这样做的目的是在不知道舵机 ID 号的情况下可以通过广播 ID 查询到舵机 ID 号，但是是限制条件是，总线上只能接一个舵机，不然都会返回数据造成总线冲突。"}]}], "index": 3}, {"bbox": [88, 291, 393, 304], "type": "text", "lines": [{"bbox": [88, 291, 393, 304], "spans": [{"bbox": [88, 291, 393, 304], "type": "text", "content": "4. 指令名 SERVO_ANGLE_OFFSET_READ 指令值 19 数据长度 4:"}]}], "index": 4}, {"bbox": [88, 306, 376, 319], "type": "text", "lines": [{"bbox": [88, 306, 376, 319], "spans": [{"bbox": [88, 306, 376, 319], "type": "text", "content": "参数 1：舵机设定的偏差值，范围- 125~125，默认值 0。"}]}], "index": 5}, {"bbox": [88, 337, 391, 351], "type": "text", "lines": [{"bbox": [88, 337, 391, 351], "spans": [{"bbox": [88, 337, 391, 351], "type": "text", "content": "5. 指令名 SERVO_ANGLE_LIMIT_READ 指令值 21 数据长度 7:"}]}], "index": 6}, {"bbox": [88, 353, 492, 413], "type": "text", "lines": [{"bbox": [88, 353, 492, 413], "spans": [{"bbox": [88, 353, 492, 413], "type": "text", "content": "参数 1：最小角度的低八位。参数 2：最小角度的高八位。范围 0~1000。参数 3：最大角度的低八位。参数 4：最大角度的高八位。范围 0~1000。默认值最小角度为 0，默认值为 1000。"}]}], "index": 7}, {"bbox": [88, 431, 383, 444], "type": "text", "lines": [{"bbox": [88, 431, 383, 444], "spans": [{"bbox": [88, 431, 383, 444], "type": "text", "content": "6. 指令名 SERVO_VIN_LIMIT_READ 指令值 23 数据长度 7:"}]}], "index": 8}, {"bbox": [88, 445, 492, 524], "type": "text", "lines": [{"bbox": [88, 445, 492, 524], "spans": [{"bbox": [88, 445, 492, 524], "type": "text", "content": "参数 1：最小输入电压的低八位。参数 2：最小输入电压的高八位。范围 4500~12000 毫伏。参数 3：最大输入电压的低八位。参数 4：最大输入电压的高八位。范围 4500~12000 毫伏。默认值最小电压为 4500，最大电压为 12000"}]}], "index": 9}, {"bbox": [88, 539, 420, 568], "type": "text", "lines": [{"bbox": [88, 539, 420, 568], "spans": [{"bbox": [88, 539, 420, 568], "type": "text", "content": "7. 指令名 SERVO_TEMP_MAX_LIMIT_READ 指令值 25 数据长度 4: 参数 1：舵机内部最高温度限制，范围 50~100°C，默认值 85°C。"}]}], "index": 10}, {"bbox": [88, 587, 348, 615], "type": "text", "lines": [{"bbox": [88, 587, 348, 615], "spans": [{"bbox": [88, 587, 348, 615], "type": "text", "content": "8. 指令名 SERVO_TEMP_READ 指令值 26 数据长度 4: 参数 1：舵机内部当前温度，无默认值。"}]}], "index": 11}, {"bbox": [88, 634, 354, 678], "type": "text", "lines": [{"bbox": [88, 634, 354, 678], "spans": [{"bbox": [88, 634, 354, 678], "type": "text", "content": "9. 指令名 SERVO_VIN_READ 指令值 27 数据长度 5: 参数 1：舵机当前输入电压值的低八位。参数 2：舵机当前输入电压值的高八位，无默认值。"}]}], "index": 12}, {"bbox": [88, 696, 490, 755], "type": "text", "lines": [{"bbox": [88, 696, 490, 755], "spans": [{"bbox": [88, 696, 490, 755], "type": "text", "content": "10. 指令名 SERVO_POS_READ 指令值 28 数据长度 5: 参数 1：舵机当前角度位置值的低八位。参数 2：舵机当前角度位置值的高八位，无默认值。说明：返回的角度位置值要转换成 signed short int 型数据，因为读出的角度"}]}], "index": 13}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 9}, {"para_blocks": [{"bbox": [89, 72, 150, 85], "type": "text", "lines": [{"bbox": [89, 72, 150, 85], "spans": [{"bbox": [89, 72, 150, 85], "type": "text", "content": "可能为负值"}]}], "index": 0}, {"bbox": [88, 104, 507, 196], "type": "text", "lines": [{"bbox": [88, 104, 507, 196], "spans": [{"bbox": [88, 104, 507, 196], "type": "text", "content": "11. 指令名 SERVO_OR_MOTOR_MODE_READ 指令值 30 数据长度 7: 参数 1: 当前舵机的模式, 0 为位置控制模式, 1 为电机控制模式, 默认 0, 参数 2: 空值, 设置成 0 即可。参数 3: 转动速度值的低八位。参数 4: 转动速度值的高八位。范围 -1000~1000, 只在电机控制模式时有效, 控制电机的转速, 该值为负值代表反转, 正值代表正转。"}]}], "index": 1}, {"bbox": [88, 212, 502, 260], "type": "text", "lines": [{"bbox": [88, 212, 502, 260], "spans": [{"bbox": [88, 212, 502, 260], "type": "text", "content": "12. 指令名 SERVO_LOAD_OR_UNLOAD_READ 指令值 32 数据长度 4: 参数 1: 舵机内部电机是否卸载掉电, 范围 0 或 1, 0 代表卸载掉电, 此时舵机无力矩输出。1 代表装载电机, 此时舵机有力矩输出, 默认值 0。"}]}], "index": 2}, {"bbox": [89, 275, 501, 321], "type": "text", "lines": [{"bbox": [89, 275, 501, 321], "spans": [{"bbox": [89, 275, 501, 321], "type": "text", "content": "13. 指令名 SERVO_LED_CTRL_READ 指令值 34 数据长度 4: 参数 1: LED 灯的亮灭状态, 范围 0 或 1, 0 代表 LED 常亮。1 代表 LED 常灭, 默认 0。"}]}], "index": 3}, {"bbox": [89, 337, 505, 385], "type": "text", "lines": [{"bbox": [89, 337, 505, 385], "spans": [{"bbox": [89, 337, 505, 385], "type": "text", "content": "14. 指令名 SERVO_LED_ERROR_READ 指令值 36 数据长度 4: 参数 1: 舵机哪些故障会导致 LED 闪烁报警的值, 范围 0~7。其数值与故障对应关系见表 3。"}]}], "index": 4}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 10}, {"para_blocks": [{"bbox": [89, 68, 171, 85], "type": "title", "lines": [{"bbox": [89, 68, 171, 85], "spans": [{"bbox": [89, 68, 171, 85], "type": "text", "content": "4.校验码计算"}]}], "index": 0, "level": 1}, {"bbox": [89, 89, 195, 104], "type": "text", "lines": [{"bbox": [89, 89, 195, 104], "spans": [{"bbox": [89, 89, 195, 104], "type": "text", "content": "1）打开计算器应用。"}]}], "index": 1}, {"type": "image", "bbox": [264, 105, 332, 221], "blocks": [{"bbox": [264, 105, 332, 221], "lines": [{"bbox": [264, 105, 332, 221], "spans": [{"bbox": [264, 105, 332, 221], "type": "image", "image_path": "b16736cf74b4748ffe61fc7200b1c80cd90d99420ef46db65e155e25806e2f8e.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}, {"bbox": [88, 224, 324, 238], "type": "text", "lines": [{"bbox": [88, 224, 324, 238], "spans": [{"bbox": [88, 224, 324, 238], "type": "text", "content": "2）点击右上角打开导航”，选择“程序员”。"}]}], "index": 3}, {"type": "image", "bbox": [187, 240, 408, 703], "blocks": [{"bbox": [187, 240, 408, 703], "lines": [{"bbox": [187, 240, 408, 703], "spans": [{"bbox": [187, 240, 408, 703], "type": "image", "image_path": "bbc3da4cf06f6062712320eb643146d2bae207472601cfd14cbfc8b84ebca010.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 11}, {"para_blocks": [{"bbox": [89, 85, 283, 100], "type": "text", "lines": [{"bbox": [89, 85, 283, 100], "spans": [{"bbox": [89, 85, 283, 100], "type": "text", "content": "3）点击“HEX”这一行进行输入数值。"}]}], "index": 0}, {"type": "image", "bbox": [187, 103, 408, 331], "blocks": [{"bbox": [187, 103, 408, 331], "lines": [{"bbox": [187, 103, 408, 331], "spans": [{"bbox": [187, 103, 408, 331], "type": "image", "image_path": "1a5c64ba55faf0e0c80c4cb038b9c509d88f449c981f6e99dc6ccb3f5b58d908.jpg"}]}], "index": 1, "type": "image_body"}], "index": 1}, {"bbox": [89, 370, 448, 384], "type": "text", "lines": [{"bbox": [89, 370, 448, 384], "spans": [{"bbox": [89, 370, 448, 384], "type": "text", "content": "4）假设我们需要使总线轮机角度转到500，时间为1000ms，命令如下："}]}], "index": 2}, {"bbox": [114, 388, 289, 401], "type": "text", "lines": [{"bbox": [114, 388, 289, 401], "spans": [{"bbox": [114, 388, 289, 401], "type": "text", "content": "55 55 01 07 01 F4 01 E8 03 16"}]}], "index": 3}, {"bbox": [89, 405, 504, 445], "type": "text", "lines": [{"bbox": [89, 405, 504, 445], "spans": [{"bbox": [89, 405, 504, 445], "type": "text", "content": "其中开头的0x55 0x55为帧头，不需要参与计算，最后的0x16为校验码，参与校验码计算的就是中间的0x01 0x07 0x01 0xF4 0x01 0xE8 0x03，我们在计算器的“HEX”一栏中将中间的值相加。"}]}], "index": 4}, {"type": "image", "bbox": [187, 449, 408, 679], "blocks": [{"bbox": [187, 449, 408, 679], "lines": [{"bbox": [187, 449, 408, 679], "spans": [{"bbox": [187, 449, 408, 679], "type": "image", "image_path": "90686a134d4c5fa2d401af74131ddf775d74cd96e79ffa95def71d4c836bd526.jpg"}]}], "index": 5, "type": "image_body"}], "index": 5}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 12}, {"para_blocks": [{"bbox": [113, 97, 374, 112], "type": "text", "lines": [{"bbox": [113, 97, 374, 112], "spans": [{"bbox": [113, 97, 374, 112], "type": "text", "content": "接着点击“按位”，点击“NOT”，将和按位取反。"}]}], "index": 0}, {"type": "image", "bbox": [188, 115, 408, 343], "blocks": [{"bbox": [188, 115, 408, 343], "lines": [{"bbox": [188, 115, 408, 343], "spans": [{"bbox": [188, 115, 408, 343], "type": "image", "image_path": "d8a4822fae07eef19a2a0ae6aace09d57d4913510f6a0ace4cc23a6129fde56d.jpg"}]}], "index": 1, "type": "image_body"}], "index": 1}, {"bbox": [114, 376, 379, 391], "type": "text", "lines": [{"bbox": [114, 376, 379, 391], "spans": [{"bbox": [114, 376, 379, 391], "type": "text", "content": "该命令的校验码就是取反后的最后两位值，即0x16。"}]}], "index": 2}, {"type": "image", "bbox": [186, 394, 408, 622], "blocks": [{"bbox": [186, 394, 408, 622], "lines": [{"bbox": [186, 394, 408, 622], "spans": [{"bbox": [186, 394, 408, 622], "type": "image", "image_path": "68fc30fd21e029f48cc3ce4406b885757979d6b0f999625c5f0e961c9df8fbd4.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}], "discarded_blocks": [], "page_size": [595, 841], "page_idx": 13}], "_backend": "vlm", "_version_name": "2.0.6"}