# 幻尔科技总线舵机通信协议

# 1.概要

采用异步串行总线通讯方式，理论多至253个机器人舵机可以通过总线组成链型，通过UART异步串行接口统一控制。每个舵机可以设定不同的节点地址，多个舵机可以统一运动也可以单个独立控制。通过异步串行接口与用户的上位机(控制器或PC机)通讯，您可对其进行参数设置、功能控制。通过异步串行接口发送指令，可以设置为电机控制模式或位置控制模式。在电机控制模式下，可以作为直流减速电机使用，速度可调；在位置控制模式下，拥有  $0 - 240^{\circ}$  的转动范围，外加  $\pm 30$  。的偏差可调范围，在此范围内具备精确位置控制性能，速度可调。只要符合协议的半双工UART异步串行接口都可以和舵机进行通讯，对舵机进行各种控制

# 2.UART接口原理图

舵机用程序代码对UART异步串行接口进行时序控制，实现半双工异步串行总线通讯，通讯波特率为115200bps，且接口简单、协议精简。在您自行设计的控制器中，用于和舵机通讯的UART接口必须如下图所示进行处理。

图1

![](images/8e416e311e119e9dd15c48899444c348c0332d79f0e4a68bf4772c29e6e84741.jpg)

# 3.指令包

指令包格式

表1：  

<table><tr><td>帧头</td><td>ID 号</td><td>数据长度</td><td>指令</td><td>参数</td><td>校验和</td></tr><tr><td>0x55 0x55</td><td>ID</td><td>Length</td><td>Cmd</td><td>Prm 1 ... Prm N</td><td>Checksum</td></tr></table>

帧头：连续收到两个  $0\mathrm{x}55$  ，表示有数据包到达。

ID：每个舵机都有一个ID号。ID号范围  $0^{\sim}253$  ，转换为十六进制  $0\mathrm{x}00^{\sim}0\mathrm{xFD}$  广播ID：ID号254(0xFE)为广播ID,若控制器发出的ID号为254(0xFE)，所有的舵机均接收指令，但都不返回应答信息，（读取舵机ID号除外，具体说明参见下面指令介绍）以防总线冲突。

数据长度：等于待发送的数据（包含本身一个字节）长度，即数据长度Length加3等于这一包指令的长度，从帧头到校验和。

指令：控制舵机的各种指令，如位置、速度控制等。

参数：除指令外需要补充的控制信息。

校验和：校验和Checksum，计算方法如下：Checksum  $= \sim (\mathrm{ID} + \mathrm{Length} + \mathrm{Cmd} + \mathrm{Prm}1 + \ldots \mathrm{PrmN})$  若括号内的计算和超出255，则取最低的一个字节，“\~”表示取反。

（校验码计算方法可查看4. 校验码计算）

# 3.指令类型

3. 指令类型指令有两种，写指令和读指令。写指令：后面一般带有参数，将相应功能的参数写进舵机，来完成某种动作。读指令：后面一般不带参数，舵机接收到读指令后会立即返回相应数据，返回的指令值和发送给舵机的“读指令”值相同，并且带有参数。所以上位机发送读指令后要立马准备将自己变为读取状态。

下表是上位机发送个舵机的指令：

表2  

<table><tr><td>指令名</td><td>指令值</td><td>数据长度</td></tr><tr><td>SERVO_MOVE_TIME_WRITE</td><td>1</td><td>7</td></tr><tr><td>SERVO_MOVE_TIME_READ</td><td>2</td><td>3</td></tr><tr><td>SERVO_MOVE_TIME_WAIT_WRITE</td><td>7</td><td>7</td></tr><tr><td>SERVO_MOVE_TIME_WAIT_READ</td><td>8</td><td>3</td></tr><tr><td>SERVO_MOVE_START</td><td>11</td><td>3</td></tr><tr><td>SERVO_MOVE_STOP</td><td>12</td><td>3</td></tr><tr><td>SERVO_ID_WRITE</td><td>13</td><td>4</td></tr><tr><td>SERVO_ID_READ</td><td>14</td><td>3</td></tr><tr><td>SERVO_ANGLE_OFFSET_ADJUST</td><td>17</td><td>4</td></tr><tr><td>SERVO_ANGLE_OFFSET_WRITE</td><td>18</td><td>3</td></tr><tr><td>SERVO_ANGLE_OFFSET_READ</td><td>19</td><td>3</td></tr><tr><td>SERVO_ANGLE_LIMIT_WRITE</td><td>20</td><td>7</td></tr><tr><td>SERVO_ANGLE_LIMIT_READ</td><td>21</td><td>3</td></tr><tr><td>SERVO_VIN_LIMIT_WRITE</td><td>22</td><td>7</td></tr><tr><td>SERVO_VIN_LIMIT_READ</td><td>23</td><td>3</td></tr><tr><td>SERVO_TEMP_MAX_LIMIT_WRITE</td><td>24</td><td>4</td></tr><tr><td>SERVO_TEMP_MAX_LIMIT_READ</td><td>25</td><td>3</td></tr><tr><td>SERVO_TEMP_READ</td><td>26</td><td>3</td></tr><tr><td>SERVO_VIN_READ</td><td>27</td><td>3</td></tr><tr><td>SERVO_POS_READ</td><td>28</td><td>3</td></tr><tr><td>SERVO_OR_MOTOR_MODE_WRITE</td><td>29</td><td>7</td></tr><tr><td>SERVO_OR_MOTOR_MODE_READ</td><td>30</td><td>3</td></tr><tr><td>SERVO_LOAD_OR_UNLOAD_WRITE</td><td>31</td><td>4</td></tr><tr><td>SERVO_LOAD_OR_UNLOAD_READ</td><td>32</td><td>3</td></tr><tr><td>SERVO_LED_CTRL_WRITE</td><td>33</td><td>4</td></tr><tr><td>SERVO_LED_CTRL_READ</td><td>34</td><td>3</td></tr><tr><td>SERVO_LED_ERROR_WRITE</td><td>35</td><td>4</td></tr><tr><td>SERVO_LED_ERROR_READ</td><td>36</td><td>3</td></tr></table>

指令名：只是为了方便识别，用户也可以根据自己的习惯自行设定。指令名后缀为“_WRITE”代表写指令，后缀为“_READ”代表读指令。

注意：在发送舵机控制指令时，需要等待前一条指令运行结束后，再发送下一条指令，否则旧指令的运行会被打断。

指令值：即表1中指令包的指令Cmd。

数据长度：即表1中数据长度Length

1．指令名SERVO_MOVE_TIME_WRITE指令值1数据长度7；参数1：角度的低八位。参数2：角度的高八位。范围0~1000，对应舵机角度的0~240°，即舵机可变化

的最小角度为0.24度。

参数3：时间低八位。

参数4：时间高八位，时间的范围  $0^{\sim}30000$  毫秒。该命令发送给舵机，舵机将在参数时间内从当前角度匀速转动到参数角度。该指令到达舵机后，舵机会立即转动。

举例：

1）使总线舵机角度转到500，时间为1000ms，命令如下：5555010701F401E803crc2）使总线舵机角度转到1000，时间为300ms，命令如下：5555010701E8032C01crc

2．指令名SERVO_MOVE_TIME_READ指令值2数据长度3：

读取指令名SERVO_MOVE_TIME_WRITE发送给舵机的角度和时间值，舵机返回给上位机的指令包详情请参见下面表4的说明。

3. 指令名SERVO_MOVE_TIME_WAIT_WRITE指令值7数据长度7：

参数1：预设角度的低八位。

参数2：预设角度的高八位。范围  $0^{\sim}1000$  ，对应舵机角度的  $0^{\sim}240^{\circ}$  ，即舵机可变化的最小角度为0.24度。

参数3：预设时间低八位。

参数4：预设时间高八位，时间的范围  $0^{\sim}30000$  毫秒。该指令跟第1点中SERVO_MOVE_TIME_WRITE指令功能相似，不同的是该指令到达舵机后，舵机不会立即转动，需要等待指令名SERVO_MOVE_START指令值为11的指令送达舵机后，舵机才会转动，将在参数时间内从当前角度匀速转动到参数角度。

举例：

1）使总线舵机角度转到500，时间为1000ms，命令如下：5555010707F401E803crc2）使总线舵机角度转到1000，时间为300ms，命令如下：5555010707E8032C01crc

4．指令名SERVO_MOVE_TIME_WAIT_READ指令值8数据长度3：

读取指令名SERVO_MOVE_TIME_WAIT_WRITE发送给舵机的预设角度和预设时间值，舵机返回给上位机的指令包详情请参见下面表3的说明。

5．指令名SERVO_MOVE_START指令值11数据长度3：配合指令SERVO_MOVE_TIME_WAIT_WRITE使用，在第3点中有说明。

举例：

1）向总线舵机1发送运行命令，如下：555501030Bcrc

6．指令名 SERVO_MOVE_STOP 指令值 12 数据长度 3: 该指令到达舵机，如果舵机正在转动，就会立即停止转动。停在当前角度位置。

举例：

1）向总线舵机1发送停止命令，如下：555501030Ccrc

7．指令名 SERVO_ID_WRITE 指令值 13 数据长度 4: 参数 1：舵机的 ID 值，范围 0~253，默认为 1。该指令会重新给舵机写入 ID 值，并且掉电保存。

举例：

1）将ID为1的舵机设置舵机ID号为255501040D02crc将ID为2的舵机设置舵机ID号为1055502040D0Acrc

8．指令名 SERVO_ID_READ 指令值 14 数据长度 3: 读取舵机的 ID 值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

9．指令名 SERVO_ANGLE_OFFSET_ADJUST 指令值 17 数据长度 4:

参数 1：舵机内部的偏差值，范围  $- 125^{\circ} - 125$  ，对应角度为  $- 30^{\circ} - 30^{\circ}$  ，该指令到达舵机，舵机将立即转动进行偏差调整。

注意 1：通过此指令调整好的偏差值不支持掉电保存，想要保存请参考第 10 点。注意 2：因为该参数为 signed char 型数据，而发送的指令包都为 unsigned char 型数据，所以发送之前要做强制转换成 unsigned char 型数据放到指令包中进行发送

举例：

1）设置总线舵机1的偏差为6：555501041106crc

10．指令名 SERVO_ANGLE_OFFSET_WRITE 指令值 18 数据长度 3: 保存偏差值，并支持掉电保存。其偏差值的调整已在第 9 点中说明。

举例：

1）设置总线舵机1的偏差为6：

5555010312crc

11. 指令名 SERVO_ANGLE_OFFSET_READ 指令值 19 数据长度 3: 读取舵机设定的偏差值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

12. 指令名 SERVO_ANGLE_LIMIT_WRITE 指令值 20 数据长度 7:

参数 1：最小角度的低八位。参数 2：最小角度的高八位。范围  $0^{\sim}1000$  。参数 3：最大角度的低八位。

参数 4：最大角度的高八位。范围  $0^{\sim}1000$  。且最小角度值要始终小于最大角度值。该命令发送给舵机，舵机的转动角度将被限制在最小与最大之间转动。并且角度限制值支持掉电保存。

举例：

1）设置总线舵机1的最小和最大角度分别为200、800，如下：5555010714C8002003crc

13. 指令名 SERVO_ANGLE_LIMIT_READ 指令值 21 数据长度 3: 读取舵机角度的限制值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

14. 指令名 SERVO_VIN_LIMIT_WRITE 指令值 22 数据长度 7:

参数 1：最小输入电压的低八位。参数 2：最小输入电压的高八位。范围  $4500^{\sim}14000$  毫伏。参数 3：最大输入电压的低八位。

参数 4：最大输入电压的高八位。范围  $4500^{\sim}14000$  毫伏。且最小输入电压值要始终小于最大输入电压值。该命令发送给舵机，舵机的输入电压将被限制在最小与最大之间。超出范围舵机的 LED 灯将会闪烁报警，（如果设置了 LED 报警）为了保护舵机，其内的电机将会处于卸载断电状态，此时舵机将不会输出力矩，并且输入电压限制值支持掉电保存。

举例：

1）设置总线舵机输入电压最小和最大值分别为5000、10000。如下：555501071688131027crc

15. 指令名 SERVO_VIN_LIMIT_READ 指令值 23 数据长度 3: 读取舵机输入电压的限制值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

16. 指令名 SERVO_TEMP_MAX_LIMIT_WRITE 指令值 24 数据长度 4:

参数 1：舵机内部最高温度限制，范围  $50^{\sim}100^{\circ}C$  ，默认值  $85^{\circ}C$  ，如果舵机内部温度超过了此值，舵机的 LED 灯将会闪烁报警，（如果设置了 LED 报警）为了保护舵机，其内的电机将会处于卸载断电状态，此时舵机将不会输出力矩，直到温度低于此值舵机会再次进入工作状态，并且此值支持掉电保存。

举例：

1）设置总线舵机1的最高温度为80，如下：555501041850crc

17. 指令名 SERVO_TEMP_MAX_LIMIT_READ 指令值 25 数据长度 3: 读取舵机内部最高温度限制的值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

18. 指令名 SERVO_TEMP_READ 指令值 26 数据长度 3: 读取舵机内部实时温度，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

19. 指令名 SERVO_VIN_READ 指令值 27 数据长度 3: 读取舵机内部当前的输入电压值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

20. 指令名 SERVO_POS_READ 指令值 28 数据长度 3: 读取舵机当前的实际角度位置值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

21. 指令名 SERVO_OR_MOTOR_MODE_WRITE 指令值 29 数据长度 7:

参数 1：舵机模式，范围0或1，0代表位置控制模式，1代表电机控制模式，默认值0，

参数2：空值，

参数3：转动速度值的低八位。

参数4：转动速度值的高八位。范围- 1000~1000，只在电机控制模式时有效，控制电机的转速，该值为负值代表反转，正值代表正转。写入的模式和速度不支持掉电保存。

# 注意：

$①$  由于转动速度为 signed short int 型数据，所以发送改指令包之前先将该数据强制转换成 unsigned short int 型数据在进行数据传输。

$②$  在指令中，参数3与参数4以16进制补码形式表示。计算方式：正数为16进制原码，负数为取二进制数按位取反再加1。例如：1000表示为：03E8，500表示为：01F4，- 1000表示为：FC18。具体计算方法可搜索补码相关知识进行了解。

举例：

1）设置总线舵机1为电机控制模式，转动速度为100，如下：55.55.01.07.1D.01.00.64.00.crc

22. 指令名 SERVO_OR_MOTOR_MODE_READ 指令值 30 数据长度 3: 读取舵机模式相关的参数，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

23. 指令名 SERVO_LOAD_OR_UNLOAD_WRITE 指令值 31 数据长度 4:

参数 1：舵机内部电机是否卸载掉电，范围 0 或 1，0 代表卸载掉电，此时舵机无力矩输出。1 代表装载电机，此时舵机有力矩输出，默认值 0。

举例：

1）使总线舵机 1 上电，如下：55 55 01 04 1F 01 crc

24. 指令名 SERVO_LOAD_OR_UNLOAD_READ 指令值 32 数据长度 3：读取舵机内部电机的状态，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

25. 指令名 SERVO_LED_CTRL_WRITE 指令值 33 数据长度 4：参数 1：LED 灯的亮灭状态，范围 0 或 1，0 代表 LED 常亮。1 代表 LED 常灭，默认 0，并且支持掉电保存。

举例：

1）使总线舵机 1 的 LED 灯亮，如下：55 55 01 04 21 00 crc

26. 指令名 SERVO_LED_CTRL_READ 指令值 34 数据长度 3：读取 LED 灯的状态，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

27. 指令名 SERVO_LED_ERROR_WRITE 指令值 35 数据长度 4：

参数 1：舵机哪些故障会导致 LED 闪烁报警的值，范围 0~7。有三种故障会导致 LED 闪烁报警，不管 LED 灯的状态是常灭还是常亮。第一种是舵机内部温度超过最大温度限制值，（此值设定在第 16 点）。第二种是舵机输入电压超过限制值，（此值设定在第 14 点）。第三种是舵机发生堵转。该值对应故障报警关系如下表：

表3  

<table><tr><td>0</td><td>没有报警</td></tr><tr><td>1</td><td>过温</td></tr><tr><td>2</td><td>过压</td></tr><tr><td>3</td><td>过温和过压</td></tr><tr><td>4</td><td>堵转</td></tr><tr><td>5</td><td>过温和堵转</td></tr><tr><td>6</td><td>过压和堵转</td></tr><tr><td>7</td><td>过温、过压和堵转</td></tr></table>

举例：

1）使总线舵机 1 的过温故障会报警，如下：55 55 01 04 23 01 crc

28. 指令名 SERVO_LED_ERROR_READ 指令值 36 数据长度 3；读取舵机故障报警值，舵机返回给上位机的指令包详情请参见下面表 4 的说明。

举例：

1）读取总线舵机1的故障值5555010324wc

表4  

<table><tr><td>指令名</td><td>指令值</td><td>数据长度</td></tr><tr><td>SERVO_MOVE_TIME_READ</td><td>2</td><td>7</td></tr><tr><td>SERVO_MOVE_TIME_WAIT_READ</td><td>8</td><td>7</td></tr><tr><td>SERVO_ID_READ</td><td>14</td><td>4</td></tr><tr><td>SERVO_ANGLE_OFFSET_READ</td><td>19</td><td>4</td></tr><tr><td>SERVO_ANGLE_LIMIT_READ</td><td>21</td><td>7</td></tr><tr><td>SERVO_VIN_LIMIT_READ</td><td>23</td><td>7</td></tr><tr><td>SERVO_TEMP_MAX_LIMIT_READ</td><td>25</td><td>4</td></tr><tr><td>SERVO_TEMP_READ</td><td>26</td><td>4</td></tr><tr><td>SERVO_VIN_READ</td><td>27</td><td>5</td></tr><tr><td>SERVO_POS_READ</td><td>28</td><td>5</td></tr><tr><td>SERVO_OR_MOTOR_MODE_READ</td><td>30</td><td>7</td></tr><tr><td>SERVO_LOAD_OR_UNLOAD_READ</td><td>32</td><td>4</td></tr><tr><td>SERVO_LED_CTRL_READ</td><td>34</td><td>4</td></tr><tr><td>SERVO_LED_ERROR_READ</td><td>36</td><td>4</td></tr></table>

表 4 是舵机返回给上位机的指令，这些指令只有在上位机给舵机发送了读取指令后舵机才会返回的指令，并且返回的指令值和上位机发给舵机的读取指令值是一致的，不同的是，此返回值都是带有参数的。返回的数据指令包与上位机发送个舵机的指令包格式是一样的，都如表 1 那样。

1. 指令名 SERVO_MOVE_TIME_READ 指令值 2 数据长度 7；参数 1：角度的低八位。参数 2：角度的高八位。范围 0~1000。参数 3：时间低八位。参数 4：时间高八位，时间的范围 0~30000 毫秒。

2. 指令名 SERVO_MOVE_TIME_READ 指令值 8 数据长度 7: 参数 1: 预设角度的低八位。参数 2: 预设角度的高八位。范围 0~1000。参数 3: 预设时间低八位。参数 4: 预设时间高八位，时间的范围 0~30000 毫秒。

3. 指令名 SERVO_ID_READ 指令值 14 数据长度 4:

参数1：舵机ID值，默认值1。

说明：ID 的读取比其他读指令多一个特殊的地方，如果该指令包的 ID 为广播 ID：254(0xFE)，舵机是会返回应答信息的，而其他的读指令在 ID 为广播 ID 时都是不返回应答信息的。这样做的目的是在不知道舵机 ID 号的情况下可以通过广播 ID 查询到舵机 ID 号，但是是限制条件是，总线上只能接一个舵机，不然都会返回数据造成总线冲突。

4. 指令名 SERVO_ANGLE_OFFSET_READ 指令值 19 数据长度 4:

参数 1：舵机设定的偏差值，范围- 125~125，默认值 0。

5. 指令名 SERVO_ANGLE_LIMIT_READ 指令值 21 数据长度 7:

参数 1：最小角度的低八位。参数 2：最小角度的高八位。范围 0~1000。参数 3：最大角度的低八位。参数 4：最大角度的高八位。范围 0~1000。默认值最小角度为 0，默认值为 1000。

6. 指令名 SERVO_VIN_LIMIT_READ 指令值 23 数据长度 7:

参数 1：最小输入电压的低八位。参数 2：最小输入电压的高八位。范围 4500~12000 毫伏。参数 3：最大输入电压的低八位。参数 4：最大输入电压的高八位。范围 4500~12000 毫伏。默认值最小电压为 4500，最大电压为 12000

7. 指令名 SERVO_TEMP_MAX_LIMIT_READ 指令值 25 数据长度 4: 参数 1：舵机内部最高温度限制，范围 50~100°C，默认值 85°C。

8. 指令名 SERVO_TEMP_READ 指令值 26 数据长度 4: 参数 1：舵机内部当前温度，无默认值。

9. 指令名 SERVO_VIN_READ 指令值 27 数据长度 5: 参数 1：舵机当前输入电压值的低八位。参数 2：舵机当前输入电压值的高八位，无默认值。

10. 指令名 SERVO_POS_READ 指令值 28 数据长度 5: 参数 1：舵机当前角度位置值的低八位。参数 2：舵机当前角度位置值的高八位，无默认值。说明：返回的角度位置值要转换成 signed short int 型数据，因为读出的角度

可能为负值

11. 指令名 SERVO_OR_MOTOR_MODE_READ 指令值 30 数据长度 7: 参数 1: 当前舵机的模式, 0 为位置控制模式, 1 为电机控制模式, 默认 0, 参数 2: 空值, 设置成 0 即可。参数 3: 转动速度值的低八位。参数 4: 转动速度值的高八位。范围 -1000~1000, 只在电机控制模式时有效, 控制电机的转速, 该值为负值代表反转, 正值代表正转。

12. 指令名 SERVO_LOAD_OR_UNLOAD_READ 指令值 32 数据长度 4: 参数 1: 舵机内部电机是否卸载掉电, 范围 0 或 1, 0 代表卸载掉电, 此时舵机无力矩输出。1 代表装载电机, 此时舵机有力矩输出, 默认值 0。

13. 指令名 SERVO_LED_CTRL_READ 指令值 34 数据长度 4: 参数 1: LED 灯的亮灭状态, 范围 0 或 1, 0 代表 LED 常亮。1 代表 LED 常灭, 默认 0。

14. 指令名 SERVO_LED_ERROR_READ 指令值 36 数据长度 4: 参数 1: 舵机哪些故障会导致 LED 闪烁报警的值, 范围 0~7。其数值与故障对应关系见表 3。

# 4.校验码计算

1）打开计算器应用。

![](images/b16736cf74b4748ffe61fc7200b1c80cd90d99420ef46db65e155e25806e2f8e.jpg)

2）点击右上角打开导航”，选择“程序员”。

![](images/bbc3da4cf06f6062712320eb643146d2bae207472601cfd14cbfc8b84ebca010.jpg)

3）点击“HEX”这一行进行输入数值。

![](images/1a5c64ba55faf0e0c80c4cb038b9c509d88f449c981f6e99dc6ccb3f5b58d908.jpg)

4）假设我们需要使总线轮机角度转到500，时间为1000ms，命令如下：

55 55 01 07 01 F4 01 E8 03 16

其中开头的0x55 0x55为帧头，不需要参与计算，最后的0x16为校验码，参与校验码计算的就是中间的0x01 0x07 0x01 0xF4 0x01 0xE8 0x03，我们在计算器的“HEX”一栏中将中间的值相加。

![](images/90686a134d4c5fa2d401af74131ddf775d74cd96e79ffa95def71d4c836bd526.jpg)

接着点击“按位”，点击“NOT”，将和按位取反。

![](images/d8a4822fae07eef19a2a0ae6aace09d57d4913510f6a0ace4cc23a6129fde56d.jpg)

该命令的校验码就是取反后的最后两位值，即0x16。

![](images/68fc30fd21e029f48cc3ce4406b885757979d6b0f999625c5f0e961c9df8fbd4.jpg)