<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753770985194_2x84oocx6" time="2025/07/29 14:36">
    <content>
      2024年电赛H题自动行驶小车核心要求：
      1. 必须使用TI MSPM0系列MCU作为主控
      2. 禁止使用摄像头和麦克纳姆轮
      3. 小车尺寸限制：25cm×15cm×15cm
      4. 只能前进不能后退
      5. 场地：220cm×120cm，两个半径40cm的半圆弧，黑色线宽1.8cm
      6. 关键技术挑战：ABCD四点间的精确路径跟踪和速度控制优化
      7. 评分重点：完成时间（最快15秒A到B，30秒完整一圈，40秒复杂路径）
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753771198619_hhyfemd5f" time="2025/07/29 14:39">
    <content>
      2025年电赛硬件趋势分析：
      1. MCU主流：32位MCU占电机驱动市场72.83%，Cortex-M0+内核72MHz，FOC运算仅6μs
      2. 国际厂商：STM、Microchip、英飞凌、瑞萨、TI为主导
      3. 国产崛起：峰岹科技、新唐科技、兆易创新GD32H75E系列
      4. 电机驱动：从L298N/L293D向TI DRV88xx系列升级，低损耗MOSFET+过流保护
      5. 传感器进化：红外/超声波→OpenMV摄像头→RPLIDAR A1激光雷达→MPU6050姿态融合
      6. 发展趋势：控制类题目向高智能化、强综合性发展
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753886793432_ngsf8qf3s" time="2025/07/30 22:46">
    <content>
      D36A双路步进电机驱动板技术分析完成：
      1. 核心芯片ATD5984，支持1/32细分，最大1.44A输出
      2. 拨码开关123控制细分(带上拉电阻)，456控制电流
      3. 控制接口：ST(PWM上升沿)、DIR(方向)、EN(使能)、ADC(电压监测)
      4. 关键参数：420RPM时不细分需1400Hz，1/32细分需44800Hz PWM
      5. 调试验证：示波器观察A+/A-互补方波，万用表无法测量
      6. 瞄准模块应用：双轴云台控制，建议1/16或1/32细分提高精度
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753888619841_u2gfl9ifd" time="2025/07/30 23:16">
    <content>
      淘晶驰串口屏技术总结完成：
      1. 产品选型：K0系列最适合电赛瞄准模块（TJC4827K043推荐）
      2. 核心优势：性价比高、功耗低、有RTC+扩展IO、成本合理
      3. 关键控件：Button、Number、Text、Gauge、Progress_bar
      4. 通信协议：UART，指令格式+0xFF0xFF0xFF结束符
      5. 开发要点：GB2312编码、波特率匹配、控件命名规范
      6. 瞄准应用：实时坐标显示、角度指示、状态监控、参数设置
      7. 集成方案：与MSPM0G3507+D36A驱动板完美配合
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753926561566_iq2pzenqn" time="2025/07/31 09:49">
    <content>
      STM32F407步进电机抖动问题诊断完成：
      1. 问题根源：实验0项目main.c第75行Screen_ProcessCommand()函数在while循环中频繁调用，干扰PWM连续输出
      2. 对比分析：官方STM32F103项目使用简单循环无干扰，实验0项目有复杂串口屏处理逻辑
      3. 解决方案：移除或条件化Screen_ProcessCommand()调用，确保TIM2双路PWM连续输出
      4. 技术要点：D36A驱动器需要连续稳定的脉冲信号，任何主循环中的复杂处理都可能导致电机抖动
      5. 修改建议：简化while(1)循环，仅保留LED指示，将串口屏处理移至定时器中断或降低调用频率
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1753928018851_jmuumx87t" time="2025/07/31 10:13">
    <content>
      STM32F407项目硬件环境更新：
      1. 调试器类型：CMSIS-DAP Debugger (非ST-Link)
      2. 下载接口：CMSIS-DAP Debugger SWD接口
      3. 电源供电：7.4V DC (非12V)，仍在D36A输入范围内(12-24V需确认兼容性)
      4. 开发库：STM32F4xx标准外设库(非HAL库)
      5. 硬件连接状态：SWD调试接口正常，电机云台机械结构已装配
      6. 技术要点：7.4V供电可能影响D36A驱动器性能，需验证是否在有效工作范围内
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754030269738_9j01kgrzk" time="2025/08/01 14:37">
    <content>
      STM32F407步进电机控制逻辑严重错误：
      1. 旋转次数异常：期望3次实际6次，怀疑Motor_A_Rotate()函数内部有重复调用或循环错误
      2. 角度精度问题：理论1.867步/度参数正确但实际角度仍不准确
      3. 用户需求：90°CW → 180°CCW → 90°CW的精确序列
      4. 问题特征：多次修改参数无效，提示根本性控制逻辑错误
      5. 紧急需要：完整代码审查和控制流程分析
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754030958713_09ek8e4yu" time="2025/08/01 14:49">
    <content>
      42步进电机参数纠正：
      1. 电机型号：42步进电机，步距角1.8°（不是42步/圈）
      2. 基础参数：不细分1圈=200步，步距角1.8°/步
      3. 1/16细分：1圈=3200步，细分后步距角=0.1125°/步
      4. 正确STEPS_PER_DEGREE：8.889步/度（不是错误的1.867）
      5. 实际测试：当前1.867参数导致90°只转约30°，证实计算错误
      6. 紧急修正：需要将参数从1.867改为8.889
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754038986673_kwqjdj1g2" time="2025/08/01 17:03">
    <content>
      E题瞄准模块高速通信方案设计：
      1. 时限要求：第二问2秒，第三问4秒，时间极其严格
      2. 推荐方案：高速UART（921600bps），DMA传输，5字节极简协议
      3. K230引脚资源：UART1(GPIO03/04)、UART2(GPIO05/06)可用
      4. 时间分配：视觉200ms+传输1ms+逆解10ms+电机1.5s+余量289ms=2s
      5. 优化策略：30fps持续更新、流水线处理、S型加速曲线
      6. 备选方案：8位并行GPIO（最快10-20μs）或SPI模拟（1.6μs/16bit）
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754039289360_5q1hh33c5" time="2025/08/01 17:08">
    <content>
      E题K230与STM32通信方案最终确定：
      1. 选择UART通信，波特率115200（最稳定可靠）
      2. 误差容限±3%，兼容性最好，调试方便
      3. 8字节协议：[0xAA][0x55][X_H][X_L][Y_H][Y_L][SUM][0xFF]
      4. 传输时间仅0.7ms，相对2秒时限完全忽略不计
      5. K230使用UART1(GPIO03/04)，MicroPython默认支持115200
      6. 时间分配：视觉200ms+传输1ms+逆解10ms+电机1500ms+余量289ms
      7. 现场调试优势明显，串口助手可直接监控
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754040219972_2hs76a87x" time="2025/08/01 17:23">
    <content>
      E题开发策略调整为分阶段进行：
      1. 第一阶段：完善云台机械和步进控制
      - 修正STEPS_PER_DEGREE参数(1.867→8.889)
      - 解决6次旋转异常问题
      - 机械零点标定和坐标系建立
      2. 第二阶段：K230脱机视觉开发
      - 蓝紫激光点HSV检测
      - A4纸张四角定位和透视变换
      - 30fps性能优化
      3. 第三阶段：系统集成
      - UART通信连接实施
      - 联调测试和参数优化
      4. 并行开发优势：模块独立验证，降低调试复杂度
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754042813028_2bai9mbqk" time="2025/08/01 18:06">
    <content>
      双电机差异化控制系统开发完成：
      1. 问题根源：电机B无PWM通道初始化，缺少控制函数，电机A速度偏快需要下调一档
      2. 解决方案：TIM8_CH4初始化，添加Motor_B_Rotate()函数，创建独立频率控制TIM8_SetFrequency_MotorA/B()
      3. 速度档位设计：电机A中低速400-1500Hz(快速定位)，电机B超低速50-200Hz(90°范围精密控制)
      4. 技术特色：差异化设计，电机A用于水平快速扫描，电机B用于90°范围内8秒/圈甚至更慢的精密垂直调整
      5. 演示效果：上电自动演示，电机A约10秒完成5档，电机B约45秒完成超慢5档，对比效果明显
      6. 系统优势：为瞄准应用提供快粗调+慢精调完美组合，满足不同控制精度需求
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754043310902_mr1efq51t" time="2025/08/01 18:15">
    <content>
      双电机SLEEP信号极性错误修正完成：
      1. 问题诊断：电机B抱死，电机A卸载，与开发目标完全相反
      2. 根本原因：SLEEP信号极性不统一，电机A使用正确逻辑（高电平使能），电机B使用错误逻辑（低电平使能）
      3. 修正措施：统一双电机SLEEP控制为高电平使能，低电平禁用
      4. 修改内容：ATD5984_Init()初始状态、Motor_B_Rotate()使能禁用逻辑、Motor_B_Enable/Disable()函数
      5. 预期效果：上电后双电机都处于禁用状态，按序执行电机A中低速5档演示，然后电机B超低速5档演示
      6. 关键教训：同一驱动器芯片的SLEEP信号极性必须统一，不能混合使用不同逻辑
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754046795845_bikdk3exz" time="2025/08/01 19:13">
    <content>
      当前双定时器独立控制方案技术状态评估：
      1. 硬件驱动层：✅双电机独立PWM控制完成，✅SLEEP信号极性统一，✅速度档位差异化设计
      2. 基础运动功能：✅电机旋转函数，✅方向控制，✅频率动态调节
      3. 系统演示：✅上电自动演示6种并行控制模式，验证双定时器架构成功
      4. 距离完整瞄准装置缺失：坐标逆解算法、K230视觉集成、激光控制、串口通信、PID闭环、机械标定、精度验证
      5. 开发阶段：第一阶段(云台机械控制)基本完成，需要进入第二阶段(视觉模块)和第三阶段(系统集成)
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754048114667_e7446m5q0" time="2025/08/01 19:35">
    <content>
      E题第二问实现逻辑明确：
      1. 初始位置：小车放在离靶子最近的正方形边线中点，画面中直接有靶子
      2. K230识别靶心坐标，判断相对画面中心(960,540)的位置
      3. 发送方向控制指令：CMD_LEFT/RIGHT/STOP/FIRE
      4. STM32接收指令执行PID控制，只控制水平方向
      5. 目标：靶心位于画面中心竖直线上(±10像素死区)
      6. 对准后GPIO控制激光器0.5秒
      7. 通信协议：[0xAA][CMD][SPEED][0xFF]
      8. 优化：速度自适应，连续3帧确认，3秒超时保护
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754049855212_n9325ivmx" time="2025/08/01 20:04">
    <content>
      E题瞄准思路重大更新完成：
      1. 发现原方案致命缺陷：完全固定竖直电机不符合二维云台要求
      2. 创新解决方案：竖直电机工作范围水平→竖直90°，双重限位保护
      3. 机械限位系统：机械限位块+对射式红外传感器，避免硬碰撞
      4. 第二问优化：预设竖直轴水平位置，专注水平精度控制
      5. 第三问完整方案：竖直轴自动归零→水平轴60°步进搜索→精确瞄准→激光发射
      6. 技术优势：真正二维控制、向后兼容、安全可靠、完全符合题意
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754052021054_19i19mhg9" time="2025/08/01 20:40">
    <content>
      E题电机控制模块封装方案：
      1. 强烈推荐封装成指令库，提高代码复用性和维护性
      2. 分层设计：基础控制层、PID控制层、高级指令层
      3. 文件结构：motor_control.c/h, motor_pid.c/h, motor_command.h
      4. 核心接口：Motor_SetSpeed(), Motor_TrackPixelOffset(), Motor_SearchTarget(), Motor_HomeVertical()
      5. 双轴支持：MotorAxis_t枚举区分水平轴和竖直轴
      6. 实现优先级：基础控制→PID封装→高级指令→搜索归零
      7. 优势：代码复用、维护方便、调试统一、扩展性强
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754052753081_wfzqch3t2" time="2025/08/01 20:52">
    <content>
      E题第二问简化逆解开发方案：
      1. 核心思路：基于固定位置(边线中点50cm)建立简化几何模型
      2. 关键公式：angle = atan(pixel_offset * distance / focal_length)
      3. 开发步骤：参数标定→逆解算法→电机控制→精度验证
      4. 现场标定：激光器对准靶心记录中心点，移动15°记录像素差值计算焦距
      5. 控制策略：像素坐标→目标角度→电机步数→精确移动
      6. 误差补偿：线性补偿因子+系统偏差修正
      7. 降级机制：超出范围时自动切换到PID控制模式
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754053311108_195hkwpzv" time="2025/08/01 21:01">
    <content>
      E题第二问最终方案确定：
      1. 放弃逆解方案：开环步进电机精度不足，单次到位不现实
      2. 采用闭环视觉反馈：K230 30fps实时反馈+STM32速度自适应控制
      3. 两段式控制：粗调0.5s(高速接近)+细调1.0s(低速精调)+稳定0.3s+余量0.2s
      4. 速度自适应：偏差200+→800步/s，100-200→400步/s，50-100→200步/s，&lt;50→100步/s
      5. 优势：不依赖机械精度，自动补偿误差，60次调整机会，鲁棒性强
      6. 关键：连续视觉反馈比单次理论计算更适合开环系统
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754072478693_ih8eku1qu" time="2025/08/02 02:21">
    <content>
      返回瓶颈期项目代码分析完成：
      1. 项目结构：STM32F407+K230通信瞄准控制系统，版本V4.0
      2. 主要模块：K230_COMM通信模块、MOTOR_CONTROL电机控制封装、ATD5984底层驱动
      3. 协议设计：[0xAA][CMD][OFFSET_H][OFFSET_L][CHECKSUM][0xFF] 6字节格式
      4. PID控制：完整实现Kp=1.0, Ki=0.2, Kd=0.1，死区±10像素，速度映射400-1500Hz
      5. 关键缺失：K230串口接收状态机函数未实现，编译无法通过
      6. 电机封装：完整的双轴统一接口，状态管理，参数检查，速度分级控制
      7. 距离E题第二问：需要补全串口接收状态机和K230视觉模块开发
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754072883879_64cvxht6z" time="2025/08/02 02:28">
    <content>
      STM32侧开发任务快速解决完成：
      1. 问题诊断：项目95%完成，仅缺失system_tick_ms全局变量定义
      2. 解决方案：在main.c添加system_tick_ms变量和SysTick_Handler()中断函数
      3. 具体实现：每1ms递增计数器，为K230通信超时检测提供时间基准
      4. 技术要点：利用SysTick定时器中断，无需修改delay.c现有代码
      5. 完成状态：STM32侧通信模块现已100%完成，可立即进行编译验证
      6. 下一步：Keil编译验证 + K230视觉模块开发
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754092050729_w1ojy72sf" time="2025/08/02 07:47">
    <content>
      用户已将K230端视觉代码修改为发送矩形中心坐标格式：
      1. 修改了分辨率为600x360提高精度
      2. 移除了复杂的椭圆坐标生成和透视变换
      3. 简化为发送矩形中心坐标：send_rect_center()函数发送&quot;x,y&quot;格式
      4. 保留&quot;NO&quot;发送当无矩形检测时
      5. 用户询问在当前K230字符串发送vs STM32二进制接收格式冲突下，哪种修改开发进度更快
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754092877168_ja9a16dgg" time="2025/08/02 08:01">
    <content>
      用户对K230视觉代码进行了重要修改：
      1. 新增CENTER_THRESHOLD = 5（中心区域阈值）
      2. 修改发送函数为send_target_info(center_x, screen_center_x)
      3. 发送格式改为：&quot;方向,距离&quot; - 如&quot;center,2&quot;、&quot;left,50&quot;、&quot;right,30&quot;
      4. 添加画面中心竖直线绘制作为参考线
      5. 判断逻辑：距离≤5像素为&quot;center&quot;，否则为&quot;left&quot;或&quot;right&quot;
      6. 发送实际像素距离值而非固定坐标
      这个修改更符合E题第二问的瞄准需求，STM32端需要相应调整解析逻辑
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754095412488_uos9jl2h1" time="2025/08/02 08:43">
    <content>
      用户要求实现E题第2问完整功能：按下板载按键K0(PE4引脚)启动，K230发送识别信息给STM32，STM32控制电机旋转使靶心像素点位于画面竖直中心线上后停下。这是从当前基础代码到完整第2问功能的关键实现步骤。
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754116269763_3kdny1yq5" time="2025/08/02 14:31">
    <content>
      用户有两个版本的工程项目混淆了：
      1. &quot;##版本尾声-通讯开发后&quot;：包含KEY按键模块、完整的状态机控制、串口自动测试功能、瞄准完成回调机制，主程序有完整的按键状态切换逻辑
      2. &quot;####版本尾声-通讯开发后 - 副本&quot;：缺少KEY按键模块，主程序简化为纯K230通信处理，无状态控制机制
    
      K230视觉代码&quot;25e视觉.py&quot;仅存在于第一个版本中，实现了完整的&quot;方向,距离&quot;格式通信协议，支持START/STOP/COMPLETED指令，有激光器控制和矩形识别功能。
    
      第一个版本更适合实现E题第二问的按键启动瞄准功能。
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754116540730_28c2ui65n" time="2025/08/02 14:35">
    <content>
      E题&quot;##版本尾声-通讯开发后&quot;项目完整分析完成：
      1. 项目结构完整：STM32F407主控+K230视觉模块，包含完整的HAREWARE模块封装
      2. STM32端功能：按键控制状态机、K230通信模块、双轴电机控制、PID算法、激光器控制
      3. K230端功能：矩形检测、中心坐标计算、方向判断、串口通信、激光器同步控制
      4. 通信协议兼容：K230发送字符串格式&quot;left,50&quot;、&quot;center,2&quot;，STM32完全支持解析
      5. 核心逻辑完备：按键启动→视觉识别→坐标计算→方向控制→电机调整→激光发射→自动停止
      6. 关键缺失：K230_IsStringReady()、K230_GetString()、K230_ClearStringFlag()函数未实现
      7. 实现可行性：95%功能完成，仅需补全3个字符串接收函数即可完整实现第二问
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754116583108_pcq7zywr2" time="2025/08/02 14:36">
    <content>
      STM32与K230通信实现情况分析：
      ✅ 已实现部分：
      1. STM32端：完整的K230_COMM模块，PID控制，电机控制接口，激光器GPIO控制
      2. STM32端：字符串接收状态机(K230_ParseString)，K230_IsStringReady()，K230_GetString()函数
      3. STM32端：处理&quot;方向,距离&quot;格式的K230_Process_Frame()函数
      4. K230端：完整的25e视觉.py，支持START/STOP/COMPLETED指令，发送&quot;方向,距离&quot;格式
    
      ⚠️ 协议不匹配问题：
      - STM32头文件设计为6字节二进制协议[0xAA][CMD][OFFSET_H][OFFSET_L][CHECKSUM][0xFF]
      - 实际实现为字符串协议&quot;left,50&quot;/&quot;right,30&quot;/&quot;center,5&quot;
      - K230发送格式与STM32解析格式完全匹配
    
      ✅ 通信已完全实现，两端协议匹配，可直接运行测试
    </content>
    <tags>#其他</tags>
  </item>
</memory>