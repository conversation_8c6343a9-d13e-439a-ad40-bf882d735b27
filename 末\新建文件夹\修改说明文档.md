# K230电赛视觉追踪系统 - 舵机通信协议V2.0修改说明

## 📋 项目概述

这是一个基于K230芯片的智能目标追踪系统，专门为2025年电子设计竞赛开发。系统能够实时检测目标并通过双轴舵机云台进行精确追踪。

## 🔧 硬件引脚接线

### K230开发板引脚连接

| 功能 | K230引脚 | 连接设备 | 说明 |
|------|----------|----------|------|
| UART3_TXD | GPIO50 | 舵机控制线 | 发送舵机控制指令 |
| UART3_RXD | GPIO51 | 舵机反馈线 | 接收舵机状态反馈 |
| 摄像头接口 | CSI2 | 摄像头模块 | 图像采集 |
| 显示接口 | DSI | LCD显示屏 | 实时显示 |
| 电源 | 5V/GND | 舵机电源 | 舵机供电 |

### 舵机连接说明

```
K230 UART3 ←→ 舵机总线
├── TXD (GPIO50) → 舵机数据线
├── RXD (GPIO51) ← 舵机反馈线  
├── GND → 舵机地线
└── 5V → 舵机电源线
```

**注意事项：**
- 舵机需要独立5V电源供电，电流需求较大
- 数据线建议使用屏蔽线，避免干扰
- 确保K230和舵机共地连接

## 🚀 主要修改内容

### 1. 舵机通信协议更新

**原协议 → 新协议V2.0**

| 项目 | 原协议 | 新协议V2.0 |
|------|--------|------------|
| 角度范围 | 0-4095 (4096分度) | 0-1000 (1000分度) |
| 角度对应 | 0-270° | 0-240° |
| 中心位置 | 2048 (135°) | 500 (120°) |
| 通信格式 | 自定义格式 | 标准帧格式 |

**新协议帧格式：**
```
[0x55][0x55][ID][Length][Cmd][Param1][Param2][Param3][Param4][Checksum]
```

### 2. 舵机配置参数修改

```python
# 原配置
self.X_CENTER = 2048    # 135度中心
self.X_MIN_POS = 1024   # 约67.5度
self.X_MAX_POS = 3072   # 约202.5度

# 新配置  
self.X_CENTER = 500     # 120度中心 (0度逻辑中心)
self.X_MIN_POS = 0      # 0度 (-120度相对中心)
self.X_MAX_POS = 1000   # 240度 (+120度相对中心)
```

### 3. 通信指令实现

**新增函数：**
- `calculate_checksum()` - 校验和计算
- `send_servo_command()` - 标准协议指令发送

**指令格式示例：**
```python
# 舵机移动到500位置，用时1000ms
# 55 55 01 07 01 F4 01 E8 03 16
指令包 = [0x55, 0x55, 0x01, 0x07, 0x01, 0xF4, 0x01, 0xE8, 0x03, 0x16]
```

### 4. 角度转换逻辑

```python
# 新协议角度转换
def position_to_angle(position):
    """位置值转换为实际角度"""
    return (position - 500) * 240 / 1000

# 示例：
# position=0   → angle=-120°
# position=500 → angle=0°  
# position=1000 → angle=+120°
```

## 📊 系统架构分析

### 整体思路流程

```mermaid
graph TD
    A[系统启动] --> B[初始化硬件]
    B --> C[加载AI模型]
    C --> D[舵机回归中心]
    D --> E[开始主循环]
    
    E --> F[图像采集]
    F --> G[AI目标检测]
    G --> H{检测到目标?}
    
    H -->|是| I[计算目标位置]
    H -->|否| J[继续搜索]
    
    I --> K[PID控制计算]
    K --> L[舵机位置更新]
    L --> M[发送舵机指令]
    
    J --> N[显示搜索状态]
    M --> N
    N --> O[UI界面更新]
    O --> P[FPS计算]
    P --> E
```

### 核心算法

1. **目标检测**：基于深度学习的实时目标检测
2. **PID控制**：精确的位置控制算法
3. **动态响应**：根据误差大小调整移动速度
4. **死区控制**：避免微小抖动

## 🎯 性能优化

### 响应速度优化

| 参数 | 原值 | 优化值 | 说明 |
|------|------|--------|------|
| 舵机更新频率 | 150ms | 80ms | 提高响应速度 |
| PID Kp参数 | 0.15 | 0.25 | 增强响应性 |
| 最快移动时间 | 50ms | 10ms | 大误差快速响应 |
| 最慢移动时间 | 5000ms | 300ms | 避免过慢补偿 |

### 稳定性保证

- **死区控制**：±8像素死区避免抖动
- **PID微分项**：Kd=0.08提供阻尼
- **位置限制**：严格的角度范围限制
- **错误处理**：完善的异常捕获机制

## 🔍 调试功能

### 实时显示信息

- **FPS显示**：实时帧率监控
- **舵机位置**：当前X/Y轴位置和角度
- **目标信息**：检测目标的位置坐标
- **追踪状态**：搜索中/追踪中状态

### 调试模式

```python
debug_mode = 1  # 开启调试输出
```

开启后显示：
- 图像处理耗时
- AI推理耗时  
- 舵机指令详情
- 系统状态信息

## ⚙️ 配置参数

### 舵机参数配置

```python
# 舵机ID设置
self.SERVO_X_ID = 1  # X轴舵机ID
self.SERVO_Y_ID = 2  # Y轴舵机ID

# Y轴方向控制
self.Y_AXIS_INVERTED = False  # Y轴是否反向
```

### AI模型配置

```python
# 模型路径和参数
self.kmodel_name = "model.kmodel"
self.confidence_threshold = 0.5  # 置信度阈值
self.nms_threshold = 0.3         # NMS阈值
```

## 🚨 注意事项

### 硬件要求

1. **K230开发板**：确保固件版本兼容
2. **舵机型号**：支持新通信协议V2.0的总线舵机
3. **电源供应**：舵机需要独立5V/2A以上电源
4. **摄像头**：支持RGB565格式输出

### 使用建议

1. **首次使用**：先测试舵机单独控制
2. **调试模式**：开启debug_mode查看详细信息
3. **参数调优**：根据实际情况调整PID参数
4. **安全操作**：确保舵机运动范围内无障碍物

### 常见问题

1. **舵机不响应**：检查接线和波特率设置
2. **追踪不稳定**：调整PID参数和死区大小
3. **检测精度低**：检查AI模型和置信度阈值
4. **系统卡顿**：检查内存使用和垃圾回收

## 📝 版本信息

- **原版本**：基于4096分度舵机协议
- **当前版本**：基于新通信协议V2.0 (0-1000分度)
- **修改日期**：2025年
- **适用竞赛**：2025年电子设计竞赛视觉追踪题目

## 🎉 总结

本次修改主要针对舵机通信协议进行了全面升级，从原来的4096分度系统改为标准的1000分度系统，同时优化了响应速度和稳定性。新系统具有更好的兼容性和更高的控制精度，适合电子设计竞赛的高精度要求。
