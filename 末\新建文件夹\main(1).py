import time
from media.sensor import *
from media.display import *
from media.media import *
from machine import UART, FPIOA
import os
import ujson
import aicube
import nncase_runtime as nn
import ulab.numpy as np
import image
import gc

# 颜色盘 (从 ai1.py 复制过来)
color_four = [(255, 220, 20, 60), (255, 119, 11, 32), (255, 0, 0, 142), (255, 0, 0, 230),
        (255, 106, 0, 228), (255, 0, 60, 100), (255, 0, 80, 100), (255, 0, 0, 70),
        (255, 0, 0, 192), (255, 250, 170, 30), (255, 100, 170, 30), (255, 220, 220, 0),
        (255, 175, 116, 175), (255, 250, 0, 30), (255, 165, 42, 42), (255, 255, 77, 255),
        (255, 0, 226, 252), (255, 182, 182, 255), (255, 0, 82, 0), (255, 120, 166, 157),
        (255, 110, 76, 0), (255, 174, 57, 255), (255, 199, 100, 0), (255, 72, 0, 118),
        (255, 255, 179, 240), (255, 0, 125, 92), (255, 209, 0, 151), (255, 188, 208, 182),
        (255, 0, 220, 176), (255, 255, 99, 164), (255, 92, 0, 73), (255, 133, 129, 255),
        (255, 78, 180, 255), (255, 0, 228, 0), (255, 174, 255, 243), (255, 45, 89, 255),
        (255, 134, 134, 103), (255, 145, 148, 174), (255, 255, 208, 186),
        (255, 197, 226, 255), (255, 171, 134, 1), (255, 109, 63, 54), (255, 207, 138, 255),
        (255, 151, 0, 95), (255, 9, 80, 61), (255, 84, 105, 51), (255, 74, 65, 105),
        (255, 166, 196, 102), (255, 208, 195, 210), (255, 255, 109, 65), (255, 0, 143, 149),
        (255, 179, 0, 194), (255, 209, 99, 106), (255, 5, 121, 0), (255, 227, 255, 205),
        (255, 147, 186, 208), (255, 153, 69, 1), (255, 3, 95, 161), (255, 163, 255, 0),
        (255, 119, 0, 170), (255, 0, 182, 199), (255, 0, 165, 120), (255, 183, 130, 88),
        (255, 95, 32, 0), (255, 130, 114, 135), (255, 110, 129, 133), (255, 166, 74, 118),
        (255, 219, 142, 185), (255, 79, 210, 114), (255, 178, 90, 62), (255, 65, 70, 15),
        (255, 127, 167, 115), (255, 59, 105, 106), (255, 142, 108, 45), (255, 196, 172, 0),
        (255, 95, 54, 80), (255, 128, 76, 255), (255, 201, 57, 1), (255, 246, 0, 122),
        (255, 191, 162, 208)]

root_path = "/sdcard/the_model/"
config_path = root_path + "deploy_config.json"
debug_mode = 1

class ScopedTiming:
    def __init__(self, info="", enable_profile=True):
        self.info = info
        self.enable_profile = enable_profile

    def __enter__(self):
        if self.enable_profile:
            self.start_time = time.ticks_ms()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if self.enable_profile:
            elapsed_time = time.ticks_ms() - self.start_time
            print(f"{self.info} took {elapsed_time / 1:.2f} ms")

class PIDController:
    """PID 控制器"""
    def __init__(self, kp, ki, kd, output_min, output_max):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.output_min = output_min
        self.output_max = output_max
        self.integral = 0
        self.last_error = 0
        self.last_time = time.ticks_ms()

    def update(self, error):
        current_time = time.ticks_ms()
        dt = (current_time - self.last_time) / 1000.0  # Convert to seconds
        if dt == 0:
            return 0

        # 比例项
        proportional = self.kp * error

        # 积分项
        self.integral += error * dt
        if self.ki != 0:
            self.integral = max(min(self.integral, self.output_max / self.ki), self.output_min / self.ki)
        else:
            self.integral = 0

        # 微分项
        derivative = self.kd * (error - self.last_error) / dt

        # 计算输出
        output = proportional + self.ki * self.integral + derivative

        output = max(self.output_min, min(self.output_max, output))

        self.last_error = error
        self.last_time = current_time
        return output

    def reset(self):
        self.integral = 0
        self.last_error = 0
        self.last_time = time.ticks_ms()


def read_deploy_config(config_path):
    with open(config_path, 'r') as json_file:
        try:
            config = ujson.load(json_file)
        except ValueError as e:
            print("JSON 解析错误:", e)
            config = {}
    return config

class K230TrackingSystem:
    """K230 目标追踪系统"""

    def __init__(self):
        # 舵机参数
        self.SERVO_X_ID = 0x01
        self.SERVO_Y_ID = 0x02

        # 舵机是4096分度，范围是0-4095
        self.X_CENTER = 2048
        self.X_MIN_POS = 1024
        self.X_MAX_POS = 3072

        self.Y_CENTER = 2048
        self.Y_MIN_POS = 1200
        self.Y_MAX_POS = 2900

        # --- Y轴方向控制标志位 ---
        self.Y_AXIS_INVERTED = False # <-- **如果Y轴反向，请在这里修改 True/False**


        # 摄像头和显示参数
        self.DISPLAY_WIDTH = 800
        self.DISPLAY_HEIGHT = 480
        self.picture_width = 800
        self.picture_height = 480
        self.sensor_id = 2

        # AI模型相关参数
        self.deploy_conf = read_deploy_config(config_path)
        self.kmodel_name = self.deploy_conf.get("kmodel_path", "model.kmodel")
        self.labels = self.deploy_conf.get("categories", [])
        self.confidence_threshold = self.deploy_conf.get("confidence_threshold", 0.5)
        self.nms_threshold = self.deploy_conf.get("nms_threshold", 0.3)
        self.img_size = self.deploy_conf.get("img_size", [320, 320])
        self.num_classes = self.deploy_conf.get("num_classes", 80)
        self.nms_option = self.deploy_conf.get("nms_option", True)
        self.model_type = self.deploy_conf.get("model_type", "AnchorBaseDet")
        self.anchors = []
        if self.model_type == "AnchorBaseDet":
            if "anchors" in self.deploy_conf and len(self.deploy_conf["anchors"]) > 0:
                for anchor_list in self.deploy_conf["anchors"]:
                    self.anchors.extend(anchor_list)
        self.kmodel_frame_size = self.img_size
        self.frame_size = [self.picture_width, self.picture_height]
        self.strides = [8, 16, 32]

        self.ori_w = self.picture_width
        self.ori_h = self.picture_height
        self.width_ai = self.kmodel_frame_size[0]
        self.height_ai = self.kmodel_frame_size[1]
        ratiow = float(self.width_ai) / self.ori_w
        ratioh = float(self.height_ai) / self.ori_h
        self.ratio_ai = min(ratiow, ratioh)
        self.new_w_ai = int(self.ratio_ai * self.ori_w)
        self.new_h_ai = int(self.ratio_ai * self.ori_h)
        self.dw_ai = float(self.width_ai - self.new_w_ai) / 2
        self.dh_ai = float(self.height_ai - self.new_h_ai) / 2
        self.top_ai = int(round(self.dh_ai - 0.1))
        self.bottom_ai = int(round(self.dh_ai + 0.1))
        self.left_ai = int(round(self.dw_ai - 0.1))
        self.right_ai = int(round(self.dw_ai - 0.1))

        self.frame_count = 0
        self.fps_start_time = time.ticks_ms()
        self.current_fps = 0

        self.current_x_pos = self.X_CENTER
        self.current_y_pos = self.Y_CENTER

        # PID 控制器 - 优化参数提高响应速度
        # 提高Kp增强响应性，适当增加Kd保持稳定性
        self.pid_x = PIDController(kp=0.25, ki=0.0000, kd=0.08, output_min=-200, output_max=200) # 提高Kp和输出范围
        self.pid_y = PIDController(kp=0.25, ki=0.0000, kd=0.08, output_min=-200, output_max=200) # 提高Kp和输出范围

        # 死区和舵机更新间隔 - 提高更新频率
        self.deadzone_x = 8   # 像素，稍微减小死区提高精度
        self.deadzone_y = 8   # 像素，稍微减小死区提高精度  
        self.servo_update_interval_ms = 80 # 舵机更新频率提高，从150ms降低到80ms

        self.last_servo_update_time = time.ticks_ms()

        # --- 舵机动态移动时间参数 ---
        # 大幅优化移动时间，解决补偿到中心过慢的问题
        self.MIN_SERVO_MOVE_TIME_MS = 10   # 最快移动时间 (大误差时使用)
        self.MAX_SERVO_MOVE_TIME_MS = 300  # 最慢移动时间 (小误差时使用，从5000ms降低到300ms)
        
        # 动态 move_time 映射区间
        # 误差超过此值时，move_time 接近 MIN_SERVO_MOVE_TIME_MS
        self.FAST_SPEED_ERROR_THRESHOLD = max(self.picture_width // 6, self.picture_height // 6) # 约133px，更容易触发快速移动
        # 误差小于此值时，move_time 接近 MAX_SERVO_MOVE_TIME_MS  
        self.SMALL_ERROR_THRESHOLD_FOR_SPEED = max(self.deadzone_x, self.deadzone_y) + 20 # 30px，扩大快速移动范围


        # 初始化
        self.init_servo()
        self.init_camera_and_ai()

        print("🎯 K230 目标追踪系统初始化完成")
        print("硬件配置：")
        print(f"  - 显示分辨率：{self.DISPLAY_WIDTH}x{self.DISPLAY_HEIGHT}")
        print(f"  - 摄像头分辨率：{self.picture_width}x{self.picture_height} (AI处理分辨率)")
        print("舵机配置：")
        print(f"  - X轴范围：{self.X_MIN_POS}-{self.X_MAX_POS} (270度，左右各135度)")
        print(f"  - Y轴范围：{self.Y_MIN_POS}-{self.Y_MAX_POS} (150度，上60度下90度)")
        print(f"  - X轴中心：{self.X_CENTER} (0度)")
        print(f"  - Y轴中心：{self.Y_CENTER} (0度)")
        print("AI模型配置：")
        print(f"  - 模型路径：{self.kmodel_name}")
        print(f"  - 输入图像尺寸：{self.img_size[0]}x{self.img_size[1]}")
        print("功能说明：")
        print("  - 启动时：舵机自动回归中心")
        print("  - 运行中：显示实时帧率、舵机位置、目标信息和追踪状态")
        print(f"  - 舵机智能分段移动时间：10ms(超快) → 50ms(快) → 120ms(中) → 200ms(慢)")
        print(f"  - 舵机每 {self.servo_update_interval_ms}ms 更新一次位置 (高频响应)")
        print("  - 优化PID参数：Kp=0.25, Kd=0.08 (快速响应+稳定)")
        print("  - Ctrl+C：退出程序")

    def init_servo(self):
        """初始化舵机"""
        try:
            self.fpioa = FPIOA()
            self.fpioa.set_function(50, FPIOA.UART3_TXD)
            self.fpioa.set_function(51, FPIOA.UART3_RXD)

            self.uart = UART(UART.UART3, baudrate=115200, bits=UART.EIGHTBITS,
                            parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

            self.move_to_center()
            print("✅ 舵机初始化完成")

        except Exception as e:
            print(f"❌ 舵机初始化失败: {e}")

    def init_camera_and_ai(self):
        """初始化摄像头和AI模型"""
        try:
            self.sensor = Sensor()
            self.sensor.reset()
            self.sensor.set_hmirror(False)
            self.sensor.set_vflip(False)
            
            self.sensor.set_framesize(width = self.DISPLAY_WIDTH, height = self.DISPLAY_HEIGHT)
            self.sensor.set_pixformat(PIXEL_FORMAT_YUV_SEMIPLANAR_420)
            self.sensor.set_framesize(width = self.picture_width , height = self.picture_height, chn=CAM_CHN_ID_2)
            self.sensor.set_pixformat(PIXEL_FORMAT_RGB_888_PLANAR, chn=CAM_CHN_ID_2)
            sensor_bind_info = self.sensor.bind_info(x = 0, y = 0, chn = CAM_CHN_ID_0)
            Display.bind_layer(**sensor_bind_info, layer = Display.LAYER_VIDEO1)

            Display.init(Display.ST7701, to_ide = True)
            self.osd_img = image.Image(self.DISPLAY_WIDTH, self.DISPLAY_HEIGHT, image.ARGB8888)

            MediaManager.init()
            self.sensor.run()

            self.kpu = nn.kpu()
            self.ai2d = nn.ai2d()
            self.kpu.load_kmodel(root_path + self.kmodel_name)
            self.ai2d.set_dtype(nn.ai2d_format.NCHW_FMT,
                                   nn.ai2d_format.NCHW_FMT,
                                   np.uint8, np.uint8)
            self.ai2d.set_pad_param(True, [0,0,0,0,self.top_ai,self.bottom_ai,self.left_ai,self.right_ai], 0, [114,114,114])
            self.ai2d.set_resize_param(True, nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel )
            self.ai2d_builder = self.ai2d.build([1,3,self.ori_h,self.ori_w], [1,3,self.height_ai,self.width_ai])
            self.ai2d_output_tensor = nn.from_numpy(np.ones((1,3,self.height_ai,self.width_ai),dtype=np.uint8))


            print("✅ 摄像头和AI初始化完成")

        except Exception as e:
            print(f"❌ 摄像头和AI初始化失败: {e}")

    def send_servo_command(self, servo_id, position, move_time): # move_time 总是会被计算并传入
        """发送舵机命令"""
        try:
            if servo_id == self.SERVO_X_ID:
                position = max(self.X_MIN_POS, min(self.X_MAX_POS, position))
                self.current_x_pos = position
            elif servo_id == self.SERVO_Y_ID:
                position = max(self.Y_MIN_POS, min(self.Y_MAX_POS, position))
                self.current_y_pos = position

            ZT1, ZT2 = 0xFF, 0xFF
            DATA1 = 0x2A
            DATA2 = (position >> 8) & 0xFF
            DATA3 = position & 0xFF
            DATA4 = (move_time >> 8) & 0xFF
            DATA5 = move_time & 0xFF
            data_length = 0x07
            WriteDATA = 0x03

            checksum = (~(servo_id + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xFF

            cmd = bytes([ZT1, ZT2, servo_id, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, checksum])
            self.uart.write(cmd)
            return True

        except Exception as e:
            print(f"❌ 舵机命令发送失败: {e}")
            return False

    def move_to_center(self):
        """舵机回归中心位置"""
        print("🎯 舵机回归中心...")
        # 归位使用固定时间，确保平稳
        self.send_servo_command(self.SERVO_X_ID, self.X_CENTER, 1000) 
        time.sleep_ms(100)
        self.send_servo_command(self.SERVO_Y_ID, self.Y_CENTER, 1000)
        time.sleep_ms(1000)
        print("✅ 回归中心完成")

    # move_x_to_angle 和 move_y_to_angle 保持不变，因为它们现在总是会接收 move_time 参数
    def move_x_to_angle(self, angle_degrees, move_time): # move_time 必须传入
        angle_degrees = max(-135, min(135, angle_degrees))
        position = self.X_CENTER + int((angle_degrees / 135.0) * (self.X_MAX_POS - self.X_CENTER))
        return self.send_servo_command(self.SERVO_X_ID, position, move_time)

    def move_y_to_angle(self, angle_degrees, move_time): # move_time 必须传入
        angle_degrees = max(-90, min(60, angle_degrees))

        if angle_degrees <= 0:
            position = self.Y_CENTER + int((abs(angle_degrees) / 90.0) * (self.Y_MAX_POS - self.Y_CENTER))
        else:
            position = self.Y_CENTER - int((angle_degrees / 60.0) * (self.Y_CENTER - self.Y_MIN_POS))

        return self.send_servo_command(self.SERVO_Y_ID, position, move_time)

    def get_x_angle(self):
        return ((self.current_x_pos - self.X_CENTER) / (self.X_MAX_POS - self.X_CENTER)) * 135.0

    def get_y_angle(self):
        if self.current_y_pos >= self.Y_CENTER:
            return -((self.current_y_pos - self.Y_CENTER) / (self.Y_MAX_POS - self.Y_CENTER)) * 90.0
        else:
            return ((self.Y_CENTER - self.current_y_pos) / (self.Y_CENTER - self.Y_MIN_POS)) * 60.0

    def calculate_fps(self):
        self.frame_count += 1
        current_time = time.ticks_ms()

        if time.ticks_diff(current_time, self.fps_start_time) >= 1000:
            self.current_fps = self.frame_count
            self.frame_count = 0
            self.fps_start_time = current_time

    def process_frame_and_detect(self):
        rgb888p_img = self.sensor.snapshot(chn=CAM_CHN_ID_2)
        det_boxes = []
        if rgb888p_img and rgb888p_img.format() == image.RGBP888:
            with ScopedTiming("AI Inference", debug_mode > 0):
                ai2d_input = rgb888p_img.to_numpy_ref()
                ai2d_input_tensor = nn.from_numpy(ai2d_input)
                self.ai2d_builder.run(ai2d_input_tensor, self.ai2d_output_tensor)

                self.kpu.set_input_tensor(0, self.ai2d_output_tensor)
                self.kpu.run()
                results = []
                for i in range(self.kpu.outputs_size()):
                    out_data = self.kpu.get_output_tensor(i) 
                    result = out_data.to_numpy()
                    result = result.reshape((result.shape[0]*result.shape[1]*result.shape[2]*result.shape[3]))
                    del out_data
                    results.append(result)
                gc.collect()

                if self.model_type == "AnchorBaseDet":
                    det_boxes = aicube.anchorbasedet_post_process(results[0], results[1], results[2], self.kmodel_frame_size, self.frame_size, self.strides, self.num_classes, self.confidence_threshold, self.nms_threshold, self.anchors, self.nms_option)
                elif self.model_type == "GFLDet":
                    det_boxes = aicube.gfldet_post_process(results[0], results[1], results[2], self.kmodel_frame_size, self.frame_size, self.strides, self.num_classes, self.confidence_threshold, self.nms_threshold, self.nms_option)
                else:
                    det_boxes = aicube.anchorfreedet_post_process(results[0], results[1], results[2], self.kmodel_frame_size, self.frame_size, self.strides, self.num_classes, self.confidence_threshold, self.nms_threshold, self.nms_option)
            gc.collect()
        return rgb888p_img, det_boxes

    def draw_info(self, img, det_boxes, target_center_x=None, target_center_y=None):
        self.osd_img.clear()

        fps_text = f"FPS: {self.current_fps}"
        self.osd_img.draw_string_advanced(10, 10, 24, fps_text, color=(255, 255, 255), bg_color=(0, 0, 0))
        
        x_angle = self.get_x_angle()
        y_angle = self.get_y_angle()
        pos_text = f"X: {x_angle:.1f}° Y: {y_angle:.1f}°"
        self.osd_img.draw_string_advanced(10, 40, 20, pos_text, color=(255, 255, 0), bg_color=(0, 0, 0))

        range_text = f"X: -135°~+135° (270°) | Y: -90°~+60° (150°)"
        self.osd_img.draw_string_advanced(10, 70, 18, range_text, color=(0, 255, 255), bg_color=(0, 0, 0))

        if det_boxes:
            for det_boxe in det_boxes:
                x1, y1, x2, y2 = det_boxe[2], det_boxe[3], det_boxe[4], det_boxe[5]
                display_x1 = int(x1 * self.DISPLAY_WIDTH / self.picture_width)
                display_y1 = int(y1 * self.DISPLAY_HEIGHT / self.picture_height)
                display_x2 = int(x2 * self.DISPLAY_WIDTH / self.picture_width)
                display_y2 = int(y2 * self.DISPLAY_HEIGHT / self.picture_height)

                w = display_x2 - display_x1
                h = display_y2 - display_y1

                self.osd_img.draw_rectangle(display_x1, display_y1, w, h, color=color_four[det_boxe[0]][1:])
                label = self.labels[det_boxe[0]]
                score = str(round(det_boxe[1], 2))
                self.osd_img.draw_string_advanced(display_x1, display_y1 - 25, 20, label + " " + score , color=color_four[det_boxe[0]][1:])

                obj_center_x = (x1 + x2) // 2
                obj_center_y = (y1 + y2) // 2
                display_obj_center_x = int(obj_center_x * self.DISPLAY_WIDTH / self.picture_width)
                display_obj_center_y = int(obj_center_y * self.DISPLAY_HEIGHT / self.picture_height)
                self.osd_img.draw_circle(display_obj_center_x, display_obj_center_y, 3, color=(255, 0, 255), thickness= -1)

        center_x = self.DISPLAY_WIDTH // 2
        center_y = self.DISPLAY_HEIGHT // 2
        self.osd_img.draw_line(center_x - 30, center_y, center_x + 30, center_y, color=(0, 255, 0), thickness=2)
        self.osd_img.draw_line(center_x, center_y - 30, center_x, center_y + 30, color=(0, 255, 0), thickness=2)

        self.osd_img.draw_rectangle(center_x - self.deadzone_x, center_y - self.deadzone_y,
                                    self.deadzone_x * 2, self.deadzone_y * 2,
                                    color=(255, 165, 0), thickness=1)

        track_status_text = "Tracking: None"
        if target_center_x is not None and target_center_y is not None:
            track_status_text = f"Tracking: Target @ ({target_center_x},{target_center_y})"
        self.osd_img.draw_string_advanced(10, self.DISPLAY_HEIGHT - 60, 18, track_status_text, color=(255, 100, 255))

        status_text = "K230 Base Template - X:270° Y:150°(↓90°↑60°)"
        self.osd_img.draw_string_advanced(10, self.DISPLAY_HEIGHT - 30, 18, status_text, color=(255, 255, 0))

        Display.show_image(self.osd_img, 0, 0, Display.LAYER_OSD3)

    def select_target(self, det_boxes):
        """从多个检测结果中选择一个目标进行追踪 (选择置信度最高的)"""
        if not det_boxes:
            return None
        sorted_boxes = sorted(det_boxes, key=lambda x: x[1], reverse=True)
        return sorted_boxes[0]

    def run(self):
        """主运行循环"""
        print("🚀 开始运行...")
        
        try:
            while True:
                img_to_display, det_boxes = self.process_frame_and_detect()
                self.calculate_fps()

                target_box = None
                if det_boxes:
                    target_box = self.select_target(det_boxes)

                target_center_x = None
                target_center_y = None
                
                image_center_x = self.picture_width // 2
                image_center_y = self.picture_height // 2

                if target_box:
                    x1, y1, x2, y2 = target_box[2], target_box[3], target_box[4], target_box[5]
                    target_center_x = (x1 + x2) // 2
                    target_center_y = (y1 + y2) // 2
                    
                    error_x = target_center_x - image_center_x
                    error_y = target_center_y - image_center_y

                    # 优化的动态 move_time 计算 - 分段式智能时间分配
                    error_magnitude = max(abs(error_x), abs(error_y))
                    
                    # 分段式时间计算，更快响应
                    if error_magnitude >= 150:  # 大误差：超快速移动
                        calculated_move_time = 10
                    elif error_magnitude >= 80:  # 中等误差：快速移动  
                        calculated_move_time = 50
                    elif error_magnitude >= 30:  # 小误差：中速移动
                        calculated_move_time = 120
                    else:  # 微小误差：慢速精确移动
                        calculated_move_time = 200


                    # Apply deadzone before PID update
                    if abs(error_x) <= self.deadzone_x:
                        error_x = 0
                    if abs(error_y) <= self.deadzone_y:
                        error_y = 0

                    current_time = time.ticks_ms()
                    if (error_x != 0 or error_y != 0) and \
                       time.ticks_diff(current_time, self.last_servo_update_time) >= self.servo_update_interval_ms:
                        
                        delta_pos_x = int(self.pid_x.update(error_x))
                        delta_pos_y = int(self.pid_y.update(error_y))

                        # *** X 轴方向修正点：请保持您上次调试成功的符号 ***
                        # 例如，如果之前 `delta_pos_x = -delta_pos_x` 成功了，就保留。
                        # 如果是 `delta_pos_x = delta_pos_x` 成功了，就注释掉下面这行。
                        delta_pos_x = -delta_pos_x # <-- 根据您上次的成功调试，保留或注释掉这行

                        # Y轴的方向，如果Y轴也反向，根据 self.Y_AXIS_INVERTED 标志位来决定
                        if self.Y_AXIS_INVERTED:
                            delta_pos_y = -delta_pos_y
                        
                        new_x_pos = self.current_x_pos + delta_pos_x 
                        new_y_pos = self.current_y_pos + delta_pos_y 

                        new_x_pos = max(self.X_MIN_POS, min(self.X_MAX_POS, new_x_pos))
                        new_y_pos = max(self.Y_MIN_POS, min(self.Y_MAX_POS, new_y_pos))
                        
                        print(f"追踪目标: 误差 X:{error_x}px, Y:{error_y}px")
                        print(f"   PID输出 (增量，调整前): Delta X_raw:{int(self.pid_x.last_error*self.pid_x.kp)}, Y_raw:{int(self.pid_y.last_error*self.pid_y.kp)}")
                        print(f"   PID输出 (增量，调整后): Delta X_final:{delta_pos_x}, Y_final:{delta_pos_y}")
                        print(f"   舵机执行时间: {calculated_move_time}ms") # 打印动态执行时间
                        print(f"   当前舵机位置: X:{self.current_x_pos}, Y:{self.current_y_pos}")
                        print(f"   目标舵机位置: X:{new_x_pos}, Y:{new_y_pos}")
                        
                        self.send_servo_command(self.SERVO_X_ID, new_x_pos, calculated_move_time) # 传入动态计算的 move_time
                        time.sleep_ms(10)
                        self.send_servo_command(self.SERVO_Y_ID, new_y_pos, calculated_move_time) # 传入动态计算的 move_time
                        print("------ 舵机移动完成 ------")
                        self.last_servo_update_time = current_time
                    elif error_x == 0 and error_y == 0:
                        self.pid_x.reset()
                        self.pid_y.reset()

                else:
                    self.pid_x.reset()
                    self.pid_y.reset()

                self.draw_info(img_to_display, det_boxes, target_center_x, target_center_y)
                gc.collect()

        except KeyboardInterrupt:
            print("🛑 用户停止")
        except Exception as e:
            if "interrupt" in str(e).lower():
                print("🛑 IDE关闭")
            else:
                print(f"❌ 运行错误: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        try:
            nn.shrink_memory_pool()
            self.sensor.stop()
            Display.deinit()
            MediaManager.deinit()
            del self.kpu
            del self.ai2d
            del self.ai2d_builder
            del self.ai2d_output_tensor
            gc.collect()
            print("🏁 程序结束，资源已清理")
        except Exception as e:
            print(f"⚠️ 清理出错: {e}")

def main():
    print("🎯 启动 K230 目标追踪系统...")

    try:
        controller = K230TrackingSystem()
        controller.run()
    except Exception as e:
        if "interrupt" in str(e).lower():
            print("🛑 程序关闭")
        else:
            print(f"❌ 系统错误: {e}")

if __name__ == "__main__":
    main()