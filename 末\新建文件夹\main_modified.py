import time
from media.sensor import *
from media.display import *
from media.media import *
from machine import UART, FPIOA
import os
import ujson
import aicube
import nncase_runtime as nn
import ulab.numpy as np
import image
import gc

# 颜色盘 (从 ai1.py 复制过来)
color_four = [(255, 220, 20, 60), (255, 119, 11, 32), (255, 0, 0, 142), (255, 0, 0, 230),
        (255, 106, 0, 228), (255, 0, 60, 100), (255, 0, 80, 100), (255, 0, 0, 70),
        (255, 0, 0, 192), (255, 250, 170, 30), (255, 100, 170, 30), (255, 220, 220, 0),
        (255, 175, 116, 175), (255, 250, 0, 30), (255, 165, 42, 42), (255, 255, 77, 255),
        (255, 0, 226, 252), (255, 182, 182, 255), (255, 0, 82, 0), (255, 120, 166, 157),
        (255, 110, 76, 0), (255, 174, 57, 255), (255, 199, 100, 0), (255, 72, 0, 118),
        (255, 255, 179, 240), (255, 0, 125, 92), (255, 209, 0, 151), (255, 188, 208, 182),
        (255, 0, 220, 176), (255, 255, 99, 164), (255, 92, 0, 73), (255, 133, 129, 255),
        (255, 78, 180, 255), (255, 0, 228, 0), (255, 174, 255, 243), (255, 45, 89, 255),
        (255, 134, 134, 103), (255, 145, 148, 174), (255, 255, 208, 186),
        (255, 197, 226, 255), (255, 171, 134, 1), (255, 109, 63, 54), (255, 207, 138, 255),
        (255, 151, 0, 95), (255, 9, 80, 61), (255, 84, 105, 51), (255, 74, 65, 105),
        (255, 166, 196, 102), (255, 208, 195, 210), (255, 255, 109, 65), (255, 0, 143, 149),
        (255, 179, 0, 194), (255, 209, 99, 106), (255, 5, 121, 0), (255, 227, 255, 205),
        (255, 147, 186, 208), (255, 153, 69, 1), (255, 3, 95, 161), (255, 163, 255, 0),
        (255, 119, 0, 170), (255, 0, 182, 199), (255, 0, 165, 120), (255, 183, 130, 88),
        (255, 95, 32, 0), (255, 130, 114, 135), (255, 110, 129, 133), (255, 166, 74, 118),
        (255, 219, 142, 185), (255, 79, 210, 114), (255, 178, 90, 62), (255, 65, 70, 15),
        (255, 127, 167, 115), (255, 59, 105, 106), (255, 142, 108, 45), (255, 196, 172, 0),
        (255, 95, 54, 80), (255, 128, 76, 255), (255, 201, 57, 1), (255, 246, 0, 122),
        (255, 191, 162, 208)]

root_path = "/sdcard/the_model/"
config_path = root_path + "deploy_config.json"
debug_mode = 1

class ScopedTiming:
    def __init__(self, info="", enable_profile=True):
        self.info = info
        self.enable_profile = enable_profile

    def __enter__(self):
        if self.enable_profile:
            self.start_time = time.ticks_ms()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if self.enable_profile:
            elapsed_time = time.ticks_ms() - self.start_time
            print(f"{self.info} took {elapsed_time / 1:.2f} ms")

class PIDController:
    """PID 控制器"""
    def __init__(self, kp, ki, kd, output_min, output_max):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.output_min = output_min
        self.output_max = output_max
        self.integral = 0
        self.last_error = 0
        self.last_time = time.ticks_ms()

    def update(self, error):
        current_time = time.ticks_ms()
        dt = (current_time - self.last_time) / 1000.0  # Convert to seconds
        if dt == 0:
            return 0

        # 比例项
        proportional = self.kp * error

        # 积分项
        self.integral += error * dt
        if self.ki != 0:
            self.integral = max(min(self.integral, self.output_max / self.ki), self.output_min / self.ki)
        else:
            self.integral = 0

        # 微分项
        derivative = self.kd * (error - self.last_error) / dt

        # 计算输出
        output = proportional + self.ki * self.integral + derivative

        output = max(self.output_min, min(self.output_max, output))

        self.last_error = error
        self.last_time = current_time
        return output

    def reset(self):
        self.integral = 0
        self.last_error = 0
        self.last_time = time.ticks_ms()


def read_deploy_config(config_path):
    with open(config_path, 'r') as json_file:
        try:
            config = ujson.load(json_file)
        except ValueError as e:
            print("JSON 解析错误:", e)
            config = {}
    return config

class K230TrackingSystem:
    """K230 目标追踪系统 - 基于新舵机通信协议V2.0"""

    def __init__(self):
        # 舵机参数 - 根据新通信协议修改
        self.SERVO_X_ID = 1  # X轴舵机ID
        self.SERVO_Y_ID = 2  # Y轴舵机ID

        # 新舵机协议：0-1000对应0°~240°，中心为0度(500)
        self.X_CENTER = 500    # 0度中心位置 (对应120度)
        self.X_MIN_POS = 0     # -120度 (240度范围的一半)
        self.X_MAX_POS = 1000  # +120度 (240度范围的一半)

        self.Y_CENTER = 500    # 0度中心位置  
        self.Y_MIN_POS = 0     # -120度
        self.Y_MAX_POS = 1000  # +120度

        # --- Y轴方向控制标志位 ---
        self.Y_AXIS_INVERTED = False # <-- **如果Y轴反向，请在这里修改 True/False**

        # 舵机通信协议常量
        self.SERVO_FRAME_HEADER = [0x55, 0x55]  # 帧头
        self.SERVO_MOVE_TIME_WRITE = 1  # 位置控制指令

        # 摄像头和显示参数
        self.DISPLAY_WIDTH = 800
        self.DISPLAY_HEIGHT = 480
        self.picture_width = 800
        self.picture_height = 480
        self.sensor_id = 2

        # AI模型相关参数
        self.deploy_conf = read_deploy_config(config_path)
        self.kmodel_name = self.deploy_conf.get("kmodel_path", "model.kmodel")
        self.labels = self.deploy_conf.get("categories", [])
        self.confidence_threshold = self.deploy_conf.get("confidence_threshold", 0.5)
        self.nms_threshold = self.deploy_conf.get("nms_threshold", 0.3)
        self.img_size = self.deploy_conf.get("img_size", [320, 320])
        self.num_classes = self.deploy_conf.get("num_classes", 80)
        self.nms_option = self.deploy_conf.get("nms_option", True)
        self.model_type = self.deploy_conf.get("model_type", "AnchorBaseDet")
        self.anchors = []
        if self.model_type == "AnchorBaseDet":
            if "anchors" in self.deploy_conf and len(self.deploy_conf["anchors"]) > 0:
                for anchor_list in self.deploy_conf["anchors"]:
                    self.anchors.extend(anchor_list)
        self.kmodel_frame_size = self.img_size
        self.frame_size = [self.picture_width, self.picture_height]
        self.strides = [8, 16, 32]

        self.ori_w = self.picture_width
        self.ori_h = self.picture_height
        self.width_ai = self.kmodel_frame_size[0]
        self.height_ai = self.kmodel_frame_size[1]
        ratiow = float(self.width_ai) / self.ori_w
        ratioh = float(self.height_ai) / self.ori_h
        self.ratio_ai = min(ratiow, ratioh)
        self.new_w_ai = int(self.ratio_ai * self.ori_w)
        self.new_h_ai = int(self.ratio_ai * self.ori_h)
        self.dw_ai = float(self.width_ai - self.new_w_ai) / 2
        self.dh_ai = float(self.height_ai - self.new_h_ai) / 2
        self.top_ai = int(round(self.dh_ai - 0.1))
        self.bottom_ai = int(round(self.dh_ai + 0.1))
        self.left_ai = int(round(self.dw_ai - 0.1))
        self.right_ai = int(round(self.dw_ai - 0.1))

        self.frame_count = 0
        self.fps_start_time = time.ticks_ms()
        self.current_fps = 0

        self.current_x_pos = self.X_CENTER
        self.current_y_pos = self.Y_CENTER

        # PID 控制器 - 优化参数提高响应速度
        # 提高Kp增强响应性，适当增加Kd保持稳定性
        self.pid_x = PIDController(kp=0.25, ki=0.0000, kd=0.08, output_min=-200, output_max=200) # 提高Kp和输出范围
        self.pid_y = PIDController(kp=0.25, ki=0.0000, kd=0.08, output_min=-200, output_max=200) # 提高Kp和输出范围

        # 死区和舵机更新间隔 - 提高更新频率
        self.deadzone_x = 8   # 像素，稍微减小死区提高精度
        self.deadzone_y = 8   # 像素，稍微减小死区提高精度  
        self.servo_update_interval_ms = 80 # 舵机更新频率提高，从150ms降低到80ms

        self.last_servo_update_time = time.ticks_ms()

        # --- 舵机动态移动时间参数 ---
        # 大幅优化移动时间，解决补偿到中心过慢的问题
        self.MIN_SERVO_MOVE_TIME_MS = 10   # 最快移动时间 (大误差时使用)
        self.MAX_SERVO_MOVE_TIME_MS = 300  # 最慢移动时间 (小误差时使用，从5000ms降低到300ms)
        
        # 动态 move_time 映射区间
        # 误差超过此值时，move_time 接近 MIN_SERVO_MOVE_TIME_MS
        self.FAST_SPEED_ERROR_THRESHOLD = max(self.picture_width // 6, self.picture_height // 6) # 约133px，更容易触发快速移动
        # 误差小于此值时，move_time 接近 MAX_SERVO_MOVE_TIME_MS  
        self.SMALL_ERROR_THRESHOLD_FOR_SPEED = max(self.deadzone_x, self.deadzone_y) + 20 # 30px，扩大快速移动范围

        # 初始化
        self.init_servo()
        self.init_camera_and_ai()

        print("🎯 K230 目标追踪系统初始化完成")
        print("硬件配置：")
        print(f"  - 显示分辨率：{self.DISPLAY_WIDTH}x{self.DISPLAY_HEIGHT}")
        print(f"  - 摄像头分辨率：{self.picture_width}x{self.picture_height} (AI处理分辨率)")
        print("舵机配置：")
        print(f"  - X轴范围：{self.X_MIN_POS}-{self.X_MAX_POS} (240度，左右各120度)")
        print(f"  - Y轴范围：{self.Y_MIN_POS}-{self.Y_MAX_POS} (240度，上下各120度)")
        print(f"  - X轴中心：{self.X_CENTER} (0度)")
        print(f"  - Y轴中心：{self.Y_CENTER} (0度)")
        print("AI模型配置：")
        print(f"  - 模型路径：{self.kmodel_name}")
        print(f"  - 输入图像尺寸：{self.img_size[0]}x{self.img_size[1]}")
        print("功能说明：")
        print("  - 启动时：舵机自动回归中心")
        print("  - 运行中：显示实时帧率、舵机位置、目标信息和追踪状态")
        print(f"  - 舵机智能分段移动时间：10ms(超快) → 50ms(快) → 120ms(中) → 200ms(慢)")
        print(f"  - 舵机每 {self.servo_update_interval_ms}ms 更新一次位置 (高频响应)")
        print("  - 优化PID参数：Kp=0.25, Kd=0.08 (快速响应+稳定)")
        print("  - 新舵机通信协议V2.0：0-1000对应0°~240°")
        print("  - Ctrl+C：退出程序")

    def init_servo(self):
        """初始化舵机"""
        try:
            self.fpioa = FPIOA()
            self.fpioa.set_function(50, FPIOA.UART3_TXD)
            self.fpioa.set_function(51, FPIOA.UART3_RXD)

            self.uart = UART(UART.UART3, baudrate=115200, bits=UART.EIGHTBITS,
                            parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

            self.move_to_center()
            print("✅ 舵机初始化完成")

        except Exception as e:
            print(f"❌ 舵机初始化失败: {e}")

    def calculate_checksum(self, data):
        """计算校验和：~(ID + Length + Cmd + Prm1 + ... + PrmN)"""
        checksum = sum(data) & 0xFF  # 取最低字节
        return (~checksum) & 0xFF    # 按位取反

    def send_servo_command(self, servo_id, position, move_time_ms):
        """发送舵机位置控制命令 - 基于新通信协议V2.0"""
        try:
            # 限制位置范围
            position = max(0, min(1000, int(position)))
            move_time_ms = max(0, min(30000, int(move_time_ms)))

            # 构建指令包
            # 帧头：0x55 0x55
            # ID：舵机ID
            # 数据长度：7 (Length + Cmd + 4个参数)
            # 指令：1 (SERVO_MOVE_TIME_WRITE)
            # 参数1：角度低八位
            # 参数2：角度高八位
            # 参数3：时间低八位
            # 参数4：时间高八位

            pos_low = position & 0xFF
            pos_high = (position >> 8) & 0xFF
            time_low = move_time_ms & 0xFF
            time_high = (move_time_ms >> 8) & 0xFF

            # 数据部分（用于校验和计算）
            data = [servo_id, 7, self.SERVO_MOVE_TIME_WRITE, pos_low, pos_high, time_low, time_high]
            checksum = self.calculate_checksum(data)

            # 完整指令包
            command = self.SERVO_FRAME_HEADER + data + [checksum]

            # 发送指令
            self.uart.write(bytes(command))

            if debug_mode:
                print(f"舵机{servo_id}: 位置={position} 时间={move_time_ms}ms 指令={' '.join([f'{b:02X}' for b in command])}")

        except Exception as e:
            print(f"❌ 发送舵机命令失败: {e}")

    def move_to_center(self):
        """舵机回归中心位置"""
        print("🎯 舵机回归中心...")
        self.send_servo_command(self.SERVO_X_ID, self.X_CENTER, 1000)
        time.sleep(0.1)
        self.send_servo_command(self.SERVO_Y_ID, self.Y_CENTER, 1000)
        time.sleep(1.2)  # 等待舵机到位

        self.current_x_pos = self.X_CENTER
        self.current_y_pos = self.Y_CENTER
        print("✅ 舵机已回归中心")

    def calculate_dynamic_move_time(self, error_magnitude):
        """根据误差大小动态计算舵机移动时间"""
        if error_magnitude >= self.FAST_SPEED_ERROR_THRESHOLD:
            # 大误差：快速移动
            return self.MIN_SERVO_MOVE_TIME_MS
        elif error_magnitude <= self.SMALL_ERROR_THRESHOLD_FOR_SPEED:
            # 小误差：慢速移动
            return self.MAX_SERVO_MOVE_TIME_MS
        else:
            # 中等误差：线性插值
            ratio = (error_magnitude - self.SMALL_ERROR_THRESHOLD_FOR_SPEED) / \
                   (self.FAST_SPEED_ERROR_THRESHOLD - self.SMALL_ERROR_THRESHOLD_FOR_SPEED)
            move_time = self.MAX_SERVO_MOVE_TIME_MS - ratio * (self.MAX_SERVO_MOVE_TIME_MS - self.MIN_SERVO_MOVE_TIME_MS)
            return int(move_time)

    def update_servo_position(self, target_x, target_y):
        """更新舵机位置"""
        current_time = time.ticks_ms()
        if current_time - self.last_servo_update_time < self.servo_update_interval_ms:
            return

        # 计算图像中心
        center_x = self.picture_width // 2
        center_y = self.picture_height // 2

        # 计算误差
        error_x = target_x - center_x
        error_y = target_y - center_y

        # 检查死区
        if abs(error_x) < self.deadzone_x and abs(error_y) < self.deadzone_y:
            return

        # PID 控制计算
        pid_output_x = self.pid_x.update(error_x)
        pid_output_y = self.pid_y.update(error_y)

        # 计算新位置
        new_x_pos = self.current_x_pos + int(pid_output_x)
        new_y_pos = self.current_y_pos + int(pid_output_y)

        # Y轴方向控制
        if self.Y_AXIS_INVERTED:
            new_y_pos = self.current_y_pos - int(pid_output_y)

        # 限制范围
        new_x_pos = max(self.X_MIN_POS, min(self.X_MAX_POS, new_x_pos))
        new_y_pos = max(self.Y_MIN_POS, min(self.Y_MAX_POS, new_y_pos))

        # 计算误差大小用于动态移动时间
        error_magnitude = max(abs(error_x), abs(error_y))
        move_time = self.calculate_dynamic_move_time(error_magnitude)

        # 发送舵机命令
        if new_x_pos != self.current_x_pos:
            self.send_servo_command(self.SERVO_X_ID, new_x_pos, move_time)
            self.current_x_pos = new_x_pos

        if new_y_pos != self.current_y_pos:
            self.send_servo_command(self.SERVO_Y_ID, new_y_pos, move_time)
            self.current_y_pos = new_y_pos

        self.last_servo_update_time = current_time

    def init_camera_and_ai(self):
        """初始化摄像头和AI模型"""
        try:
            # 初始化摄像头
            sensor.reset()
            sensor.set_framesize(width=self.picture_width, height=self.picture_height)
            sensor.set_pixformat(sensor.RGB565)

            # 初始化显示
            Display.init(Display.VIRT, width=self.DISPLAY_WIDTH, height=self.DISPLAY_HEIGHT, fps=60)
            MediaManager.init()

            # 加载AI模型
            self.kpu = nn.load_model(root_path + self.kmodel_name)
            self.ai2d = nn.ai2d()
            self.ai2d.set_dtype(nn.ai2d_format.NCHW_FMT, nn.ai2d_format.NCHW_FMT, np.uint8, np.uint8)
            self.ai2d.set_pad_param(True, [0, 0, 0, 0, 0, 125, 0, 125], 0, [104, 117, 123])
            self.ai2d.set_resize_param(True, nn.interp_method.tf_bilinear, nn.interp_mode.half_pixel)
            self.ai2d.build([1, 3, self.picture_height, self.picture_width], [1, 3, self.img_size[1], self.img_size[0]])

            print("✅ 摄像头和AI模型初始化完成")

        except Exception as e:
            print(f"❌ 摄像头和AI模型初始化失败: {e}")

    def preprocess_image(self, rgb888_img):
        """图像预处理"""
        with ScopedTiming("图像预处理", debug_mode):
            ai2d_input = rgb888_img.to_numpy_ref()
            ai2d_output = self.ai2d.run(ai2d_input)
            return ai2d_output

    def postprocess_detections(self, results):
        """后处理检测结果"""
        with ScopedTiming("后处理", debug_mode):
            dets = aicube.anchorbasedet_post_process(results[0], [self.ori_h, self.ori_w], self.height_ai, self.width_ai, [self.top_ai, self.bottom_ai, self.left_ai, self.right_ai], self.confidence_threshold, self.nms_threshold, self.anchors, self.num_classes)
            return dets

    def draw_detections(self, img, dets):
        """绘制检测结果"""
        target_found = False
        target_x, target_y = 0, 0
        target_info = ""

        for det in dets:
            x1, y1, x2, y2 = int(det[2]), int(det[3]), int(det[4]), int(det[5])
            w = x2 - x1
            h = y2 - y1

            # 计算目标中心
            center_x = x1 + w // 2
            center_y = y1 + h // 2

            # 绘制检测框
            img.draw_rectangle(x1, y1, w, h, color=color_four[int(det[0]) % len(color_four)], thickness=4)

            # 绘制标签
            label = f"{self.labels[int(det[0])]} {det[1]:.2f}"
            img.draw_string_advanced(x1, y1 - 20, 20, label, color=(255, 255, 255, 255))

            # 绘制中心点
            img.draw_circle(center_x, center_y, 5, color=(255, 0, 0, 255), thickness=2)

            # 更新目标信息（选择第一个检测到的目标）
            if not target_found:
                target_found = True
                target_x, target_y = center_x, center_y
                target_info = f"{self.labels[int(det[0])]} ({center_x},{center_y})"

        return target_found, target_x, target_y, target_info

    def draw_ui_overlay(self, img, target_found, target_info):
        """绘制UI覆盖层"""
        # 绘制十字准星
        center_x = self.picture_width // 2
        center_y = self.picture_height // 2

        # 十字线
        img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(0, 255, 0, 255), thickness=2)
        img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(0, 255, 0, 255), thickness=2)

        # 死区显示
        img.draw_rectangle(center_x - self.deadzone_x, center_y - self.deadzone_y,
                          self.deadzone_x * 2, self.deadzone_y * 2,
                          color=(255, 255, 0, 128), thickness=1)

        # 显示信息
        y_offset = 30
        img.draw_string_advanced(10, y_offset, 24, f"FPS: {self.current_fps:.1f}", color=(255, 255, 255, 255))
        y_offset += 30

        # 舵机位置信息
        x_angle = (self.current_x_pos - 500) * 240 / 1000  # 转换为角度
        y_angle = (self.current_y_pos - 500) * 240 / 1000
        img.draw_string_advanced(10, y_offset, 24, f"舵机X: {self.current_x_pos} ({x_angle:.1f}°)", color=(255, 255, 255, 255))
        y_offset += 30
        img.draw_string_advanced(10, y_offset, 24, f"舵机Y: {self.current_y_pos} ({y_angle:.1f}°)", color=(255, 255, 255, 255))
        y_offset += 30

        # 目标信息
        if target_found:
            img.draw_string_advanced(10, y_offset, 24, f"目标: {target_info}", color=(0, 255, 0, 255))
            img.draw_string_advanced(10, y_offset + 30, 24, "状态: 追踪中", color=(0, 255, 0, 255))
        else:
            img.draw_string_advanced(10, y_offset, 24, "目标: 未发现", color=(255, 0, 0, 255))
            img.draw_string_advanced(10, y_offset + 30, 24, "状态: 搜索中", color=(255, 255, 0, 255))

    def calculate_fps(self):
        """计算FPS"""
        self.frame_count += 1
        if self.frame_count % 30 == 0:  # 每30帧计算一次FPS
            current_time = time.ticks_ms()
            elapsed_time = current_time - self.fps_start_time
            if elapsed_time > 0:
                self.current_fps = 30000.0 / elapsed_time
            self.fps_start_time = current_time

    def run(self):
        """主运行循环"""
        print("🚀 开始目标追踪...")

        try:
            while True:
                with ScopedTiming("总帧处理时间", debug_mode):
                    # 获取图像
                    rgb888_img = sensor.snapshot()
                    if rgb888_img is None:
                        continue

                    # AI推理
                    with ScopedTiming("AI推理", debug_mode):
                        ai_input = self.preprocess_image(rgb888_img)
                        results = self.kpu.run_with_reuse(ai_input, False)
                        dets = self.postprocess_detections(results)

                    # 处理检测结果
                    target_found, target_x, target_y, target_info = self.draw_detections(rgb888_img, dets)

                    # 更新舵机位置
                    if target_found:
                        self.update_servo_position(target_x, target_y)

                    # 绘制UI
                    self.draw_ui_overlay(rgb888_img, target_found, target_info)

                    # 计算FPS
                    self.calculate_fps()

                    # 显示图像
                    Display.show_image(rgb888_img)

                    # 垃圾回收
                    gc.collect()

        except KeyboardInterrupt:
            print("\n🛑 用户中断，正在退出...")
        except Exception as e:
            print(f"❌ 运行时错误: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        try:
            print("🧹 正在清理资源...")

            # 舵机回归中心
            self.move_to_center()

            # 关闭摄像头和显示
            sensor.reset()
            Display.deinit()
            MediaManager.deinit()

            print("✅ 资源清理完成")
        except Exception as e:
            print(f"❌ 清理资源时出错: {e}")


def main():
    """主函数"""
    try:
        # 创建追踪系统实例
        tracking_system = K230TrackingSystem()

        # 运行追踪系统
        tracking_system.run()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")


if __name__ == "__main__":
    main()
