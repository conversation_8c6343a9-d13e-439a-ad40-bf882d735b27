{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["mcp__promptx__promptx_init", "mcp__promptx__promptx_welcome", "mcp__promptx__promptx_action", "mcp__promptx__promptx_learn", "mcp__promptx__promptx_remember", "WebFetch(domain:www.nuedc-training.com.cn)", "Bash(node:*)", "Bash(npm:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(echo $APPDATA)", "Bash(ls:*)", "Bash(find:*)", "Bash(xcopy:*)", "<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(where python)", "mcp__promptx__promptx_recall", "Bash(copy stepmotor.c stepmotor_backup.c)", "Bash(copy stepmotor.h stepmotor_backup.h)", "Bash(cp:*)", "<PERSON><PERSON>(keilkilll.bat)", "Bash(cp:*)", "<PERSON><PERSON>(keilkilll.bat)", "<PERSON><PERSON>(dir US<PERSON>)", "<PERSON><PERSON>(sed:*)", "Bash(cmd //c:*)", "<PERSON><PERSON>(dir:*)", "Bash(attrib:*)", "Bash(cmd /c \"attrib -R \"\"C:\\Users\\<USER>\\Desktop\\25准备！\\赛题\\##版本尾声-通讯开发后\\25e视觉.py\"\"\")", "Bash(cmd /c \"cd /d \"\"C:\\Users\\<USER>\\Desktop\\25准备！\\赛题\\##版本尾声-通讯开发后\"\" && keilkilll.bat\")"], "deny": []}}